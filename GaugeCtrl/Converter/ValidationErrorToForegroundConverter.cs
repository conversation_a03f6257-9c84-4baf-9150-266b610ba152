using System;
using System.ComponentModel;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace GaugeCtrl.Converter
{
    /// <summary>
    /// 验证错误状态到前景色的转换器
    /// </summary>
    public class ValidationErrorToForegroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is IDataErrorInfo dataErrorInfo)
            {
                string error = dataErrorInfo["Value"];
                if (!string.IsNullOrEmpty(error))
                {
                    return Brushes.Red;
                }
            }
            return Brushes.Black;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}