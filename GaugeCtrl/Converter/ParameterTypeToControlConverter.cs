using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows;
using GaugeCtrl.Core.Models;

namespace GaugeCtrl.Converter
{
    /// <summary>
    /// 参数类型到控件的转换器
    /// </summary>
    public class ParameterTypeToControlConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ParameterType parameterType)
            {
                return parameterType;
            }
            return ParameterType.Numeric;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 参数值转换器，处理不同类型参数值的转换
    /// </summary>
    public class ParameterValueConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (targetType == typeof(bool?) || targetType == typeof(bool))
            {
                // 转换为布尔值
                if (value is bool boolValue)
                    return boolValue;
                if (value is int intValue)
                    return intValue != 0;
                if (value is double doubleValue)
                    return doubleValue != 0.0;
                if (value is string stringValue)
                {
                    if (bool.TryParse(stringValue, out var parsedBool))
                        return parsedBool;
                    return !string.IsNullOrEmpty(stringValue);
                }
                return false;
            }

            if (targetType == typeof(string))
            {
                // 转换为字符串
                return value?.ToString() ?? string.Empty;
            }

            // 默认返回原值
            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                if (targetType == typeof(int) || targetType == typeof(object))
                    return boolValue ? 1 : 0;
                if (targetType == typeof(double))
                    return boolValue ? 1.0 : 0.0;
                if (targetType == typeof(string))
                    return boolValue.ToString();
            }

            if (value is string stringValue)
            {
                if (targetType == typeof(int) || targetType == typeof(object))
                {
                    if (int.TryParse(stringValue, out var intResult))
                        return intResult;
                }
                if (targetType == typeof(double))
                {
                    if (double.TryParse(stringValue, out var doubleResult))
                        return doubleResult;
                }
            }

            return value;
        }
    }

    /// <summary>
    /// 布尔值可见性转换器
    /// </summary>
    public class BooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ParameterType parameterType)
            {
                string param = parameter?.ToString();
                switch (param)
                {
                    case "Boolean":
                        return parameterType == ParameterType.Boolean ? Visibility.Visible : Visibility.Collapsed;
                    case "Enum":
                        return parameterType == ParameterType.Enum ? Visibility.Visible : Visibility.Collapsed;
                    case "Numeric":
                    case "Text":
                        return (parameterType == ParameterType.Numeric || parameterType == ParameterType.Text) ? Visibility.Visible : Visibility.Collapsed;
                    default:
                        return Visibility.Collapsed;
                }
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}