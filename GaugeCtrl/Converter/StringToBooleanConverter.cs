using System;
using System.Globalization;
using System.Windows.Data;

namespace GaugeCtrl
{
    public class StringToBooleanConverter : IValueConverter
    {
        public static StringToBooleanConverter Instance { get; } = new StringToBooleanConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string selectedPage && parameter is string targetPage)
            {
                return selectedPage == targetPage;
            }
            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isChecked && isChecked && parameter is string targetPage)
            {
                return targetPage;
            }
            return Binding.DoNothing;
        }
    }

    /// <summary>
    /// 字节值转换器，用于处理ComboBox中byte类型的绑定
    /// </summary>
    public class ByteConverter : IValueConverter
    {
        public static ByteConverter Instance { get; } = new ByteConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // 从byte转换为显示值（通常是string或保持byte）
            if (value is byte byteValue)
            {
                return byteValue;
            }
            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // 从显示值转换回byte
            if (value is byte byteValue)
            {
                return byteValue;
            }

            if (value is string stringValue)
            {
                // 处理空字符串的情况
                if (string.IsNullOrEmpty(stringValue))
                {
                    return (byte)1; // 返回默认值
                }

                // 尝试解析字符串为byte
                if (byte.TryParse(stringValue, out byte result))
                {
                    return result;
                }
            }

            // 如果转换失败，返回默认值
            return (byte)1;
        }
    }
}