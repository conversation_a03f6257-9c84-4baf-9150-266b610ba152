using System;
using System.Globalization;
using System.Windows.Data;
using GaugeCtrl.Core.Models;

namespace GaugeCtrl.Converter
{
    /// <summary>
    /// 轴类型到字符串的转换器
    /// </summary>
    public class AxisTypeToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is AxisType axisType)
            {
                return axisType switch
                {
                    AxisType.Axis0 => "轴0",
                    AxisType.Axis1 => "轴1",
                    _ => value.ToString()
                };
            }
            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string str)
            {
                return str switch
                {
                    "轴0" => AxisType.Axis0,
                    "轴1" => AxisType.Axis1,
                    _ => AxisType.Axis0
                };
            }
            return AxisType.Axis0;
        }
    }
}
