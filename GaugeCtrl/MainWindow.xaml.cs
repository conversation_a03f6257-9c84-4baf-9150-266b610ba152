using System;
using HandyControl.Controls;
using HandyControl.Data;
using GaugeCtrl.Views;
using GaugeCtrl.ViewModels;
using GaugeCtrl.Helpers;
using System.Windows;
using System.Windows.Controls;

namespace GaugeCtrl
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : System.Windows.Window
    {
        public MainWindow()
        {
            InitializeComponent();
        }

        private void SideMenu_SelectionChanged(object sender, FunctionEventArgs<object> e)
        {
            if (DataContext is MainWindowViewModel viewModel && e.Info is SideMenuItem menuItem)
            {
                switch (menuItem.Tag?.ToString())
                {
                    case "SerialSettings":
                        viewModel.CurrentView = viewModel.SerialSettingsViewModel;
                        break;
                    case "Oscilloscope":
                        viewModel.CurrentView = viewModel.OscilloscopeViewModel;
                        break;
                }
            }
        }

        private void FileMenuButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.ContextMenu != null)
            {
                button.ContextMenu.PlacementTarget = button;
                button.ContextMenu.Placement = System.Windows.Controls.Primitives.PlacementMode.Bottom;
                button.ContextMenu.IsOpen = true;
            }
        }
    }
}
