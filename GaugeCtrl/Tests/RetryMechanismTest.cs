using System;
using System.Threading.Tasks;

namespace GaugeCtrl.Tests
{
    /// <summary>
    /// 重试机制测试类
    /// 用于验证参数上传下载的重试功能
    /// </summary>
    public class RetryMechanismTest
    {
        /// <summary>
        /// 测试重试机制（返回bool类型）
        /// </summary>
        public static async Task TestRetryOperationBool()
        {
            Console.WriteLine("=== 测试重试机制（bool类型） ===");
            
            // 测试1：第一次就成功
            Console.WriteLine("测试1：第一次就成功");
            var result1 = await RetryOperationAsync(
                async () => {
                    Console.WriteLine("  执行操作...");
                    await Task.Delay(10);
                    return true;
                },
                maxRetries: 3,
                delayBetweenRetries: 50,
                operationName: "测试操作1"
            );
            Console.WriteLine($"  结果: {result1}");
            Console.WriteLine();

            // 测试2：第二次成功
            Console.WriteLine("测试2：第二次成功");
            int attempt2 = 0;
            var result2 = await RetryOperationAsync(
                async () => {
                    attempt2++;
                    Console.WriteLine($"  第{attempt2}次尝试...");
                    await Task.Delay(10);
                    return attempt2 >= 2;
                },
                maxRetries: 3,
                delayBetweenRetries: 50,
                operationName: "测试操作2"
            );
            Console.WriteLine($"  结果: {result2}");
            Console.WriteLine();

            // 测试3：全部失败
            Console.WriteLine("测试3：全部失败");
            var result3 = await RetryOperationAsync(
                async () => {
                    Console.WriteLine("  执行操作（总是失败）...");
                    await Task.Delay(10);
                    return false;
                },
                maxRetries: 3,
                delayBetweenRetries: 50,
                operationName: "测试操作3"
            );
            Console.WriteLine($"  结果: {result3}");
            Console.WriteLine();
        }

        /// <summary>
        /// 测试重试机制（返回可空类型）
        /// </summary>
        public static async Task TestRetryOperationNullable()
        {
            Console.WriteLine("=== 测试重试机制（可空类型） ===");
            
            // 测试1：第一次就成功
            Console.WriteLine("测试1：第一次就成功");
            var result1 = await RetryOperationAsync<ushort>(
                async () => {
                    Console.WriteLine("  读取寄存器...");
                    await Task.Delay(10);
                    return (ushort)1234;
                },
                maxRetries: 3,
                delayBetweenRetries: 50,
                operationName: "读取寄存器1"
            );
            Console.WriteLine($"  结果: {result1}");
            Console.WriteLine();

            // 测试2：第三次成功
            Console.WriteLine("测试2：第三次成功");
            int attempt2 = 0;
            var result2 = await RetryOperationAsync<ushort>(
                async () => {
                    attempt2++;
                    Console.WriteLine($"  第{attempt2}次尝试读取寄存器...");
                    await Task.Delay(10);
                    return attempt2 >= 3 ? (ushort?)5678 : null;
                },
                maxRetries: 3,
                delayBetweenRetries: 50,
                operationName: "读取寄存器2"
            );
            Console.WriteLine($"  结果: {result2}");
            Console.WriteLine();

            // 测试3：全部失败
            Console.WriteLine("测试3：全部失败");
            var result3 = await RetryOperationAsync<ushort>(
                async () => {
                    Console.WriteLine("  读取寄存器（总是失败）...");
                    await Task.Delay(10);
                    return null;
                },
                maxRetries: 3,
                delayBetweenRetries: 50,
                operationName: "读取寄存器3"
            );
            Console.WriteLine($"  结果: {result3}");
            Console.WriteLine();
        }

        /// <summary>
        /// 重试操作的通用方法（返回bool类型）
        /// </summary>
        private static async Task<bool> RetryOperationAsync(
            Func<Task<bool>> operation,
            int maxRetries = 3,
            int delayBetweenRetries = 100,
            string operationName = "操作")
        {
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    var result = await operation();
                    if (result)
                    {
                        if (attempt > 1)
                        {
                            Console.WriteLine($"    {operationName} 在第 {attempt} 次尝试后成功");
                        }
                        return true;
                    }
                    else
                    {
                        Console.WriteLine($"    {operationName} 第 {attempt} 次尝试失败");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"    {operationName} 第 {attempt} 次尝试异常: {ex.Message}");
                }

                // 如果不是最后一次尝试，则等待后重试
                if (attempt < maxRetries)
                {
                    await Task.Delay(delayBetweenRetries);
                }
            }

            Console.WriteLine($"    {operationName} 在 {maxRetries} 次尝试后仍然失败");
            return false;
        }

        /// <summary>
        /// 重试操作的通用方法（返回可空类型）
        /// </summary>
        private static async Task<T?> RetryOperationAsync<T>(
            Func<Task<T?>> operation,
            int maxRetries = 3,
            int delayBetweenRetries = 100,
            string operationName = "操作") where T : struct
        {
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    var result = await operation();
                    if (result.HasValue)
                    {
                        if (attempt > 1)
                        {
                            Console.WriteLine($"    {operationName} 在第 {attempt} 次尝试后成功");
                        }
                        return result;
                    }
                    else
                    {
                        Console.WriteLine($"    {operationName} 第 {attempt} 次尝试返回空值");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"    {operationName} 第 {attempt} 次尝试异常: {ex.Message}");
                }

                // 如果不是最后一次尝试，则等待后重试
                if (attempt < maxRetries)
                {
                    await Task.Delay(delayBetweenRetries);
                }
            }

            Console.WriteLine($"    {operationName} 在 {maxRetries} 次尝试后仍然失败");
            return null;
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static async Task RunAllTests()
        {
            Console.WriteLine("开始测试重试机制...");
            Console.WriteLine();
            
            await TestRetryOperationBool();
            await TestRetryOperationNullable();
            
            Console.WriteLine("测试完成！");
        }
    }
}
