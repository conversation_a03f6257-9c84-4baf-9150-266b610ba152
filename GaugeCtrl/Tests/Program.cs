using System;
using System.Threading.Tasks;

namespace GaugeCtrl.Tests
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("重试机制测试程序");
            Console.WriteLine("================");
            Console.WriteLine();

            try
            {
                await RetryMechanismTest.RunAllTests();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
