using System.Windows.Controls;
using GaugeCtrl.ViewModels;

namespace GaugeCtrl.Views
{
    public partial class OscilloscopeView : UserControl
    {
        public OscilloscopeView()
        {
            InitializeComponent();
            Loaded += OscilloscopeView_Loaded;
        }

        private void OscilloscopeView_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            // 当视图加载完成后，初始化ViewModel中的图表
            if (DataContext is OscilloscopeViewModel viewModel)
            {
                viewModel.InitializePlot(MainPlot);
            }
        }
    }
}