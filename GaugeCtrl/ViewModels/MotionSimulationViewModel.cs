using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using GaugeCtrl.Core.Models;
using GaugeCtrl.Communication;
using System.Collections.ObjectModel;
using GaugeCtrl.Communication.Interfaces;
using GaugeCtrl.Helpers;
using NLog;
using System.Linq;
using System.ComponentModel.DataAnnotations;
using GaugeCtrl.Core.Interfaces;
using GaugeCtrl.Core.Services;
using System.Diagnostics;

namespace GaugeCtrl.ViewModels
{
    /// <summary>
    /// 寄存器值验证属性
    /// </summary>
    public class RegisterValueValidationAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value is ObservableCollection<RegisterData> registers)
            {
                return registers.All(r => r.ValueDec >= 0 && r.ValueDec <= 65535);
            }

            return true;
        }

        public override string FormatErrorMessage(string name)
        {
            return "寄存器值必须在0-65535之间";
        }
    }

    public partial class MotionSimulationViewModel : ObservableValidator
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private readonly ISerialPortManager? _serialPortManager;
        private readonly ICommandHistoryService _commandHistoryService;

        [ObservableProperty] [Range(0, 65535, ErrorMessage = "读取起始地址必须在0-65535之间")] [NotifyDataErrorInfo]
        private int readStartAddress;

        [ObservableProperty] [Range(1, 125, ErrorMessage = "读取数量必须在1-125之间")] [NotifyDataErrorInfo]
        private int readQuantity = 10;

        [ObservableProperty] [Range(0, 65535, ErrorMessage = "写入起始地址必须在0-65535之间")] [NotifyDataErrorInfo]
        private int writeStartAddress;

        [ObservableProperty] [Range(1, 123, ErrorMessage = "写入数量必须在1-123之间")] [NotifyDataErrorInfo]
        private int writeQuantity = 10;

        public ObservableCollection<RegisterData> ReadRegisters { get; set; }

        [RegisterValueValidation] public ObservableCollection<RegisterData> WriteRegisters { get; set; }

        /// <summary>
        /// 指令历史记录集合
        /// </summary>
        public ReadOnlyObservableCollection<CommandHistory> CommandHistories => _commandHistoryService.CommandHistories;

        /// <summary>
        /// 运动控制指令历史记录（过滤后的）
        /// </summary>
        public IEnumerable<CommandHistory> MotionCommandHistories =>
            CommandHistories.Where(h => h.FunctionCode == 0x06);

        /// <summary>
        /// 寄存器操作指令历史记录（过滤后的）
        /// </summary>
        public IEnumerable<CommandHistory> RegisterCommandHistories =>
            CommandHistories.Where(h => h.FunctionCode == 0x03 || h.FunctionCode == 0x10);

        /// <summary>
        /// 运动控制指令历史文本（格式化显示）
        /// </summary>
        public string MotionHistoryText => FormatModbusHistoryText(MotionCommandHistories);

        /// <summary>
        /// 寄存器操作指令历史文本（格式化显示）
        /// </summary>
        public string RegisterHistoryText => FormatModbusHistoryText(RegisterCommandHistories);

        public MotionSimulationViewModel(ISerialPortManager? serialPortManager = null, ICommandHistoryService? commandHistoryService = null)
        {
            _serialPortManager = serialPortManager ?? SerialPortManagerSingleton.Instance;
            _commandHistoryService = commandHistoryService ?? CommandHistoryServiceSingleton.Instance;

            ReadRegisters = new ObservableCollection<RegisterData>();
            WriteRegisters = new ObservableCollection<RegisterData>();
            UpdateReadRegisters();
            UpdateWriteRegisters();

            // 订阅WriteRegisters集合变化事件，用于验证
            WriteRegisters.CollectionChanged += (s, e) => ValidateProperty(WriteRegisters, nameof(WriteRegisters));

            // 订阅指令历史变化事件，用于更新UI
            _commandHistoryService.HistoryChanged += OnHistoryChanged;

            // 添加一些示例历史记录用于测试（仅在调试模式下）
#if DEBUG
            InitializeTestHistoryData();
#endif
        }

        /// <summary>
        /// 获取Modbus协议处理器用于运动控制
        /// </summary>
        public IModbusProtocolHandler? GetModbusHandler()
        {
            return _serialPortManager?.GetModbusHandler();
        }

        /// <summary>
        /// 获取当前配置的从站地址，如果无法获取则返回默认值1
        /// </summary>
        private byte GetSlaveAddress()
        {
            try
            {
                // 从Modbus处理器获取当前配置的从站地址
                var modbusHandler = _serialPortManager?.GetModbusHandler();
                if (modbusHandler != null)
                {
                    Logger.Debug($"使用Modbus处理器的从站地址: {modbusHandler.CurrentSlaveId}");
                    return modbusHandler.CurrentSlaveId;
                }
            }
            catch (Exception ex)
            {
                Logger.Warn(ex, "获取从站地址失败，使用默认值1");
            }

            // 默认从站地址
            return 1;
        }

        /// <summary>
        /// 指令历史变化事件处理
        /// </summary>
        private void OnHistoryChanged(object? sender, CommandHistoryChangedEventArgs e)
        {
            // 通知UI更新历史记录相关的属性
            OnPropertyChanged(nameof(CommandHistories));
            OnPropertyChanged(nameof(MotionCommandHistories));
            OnPropertyChanged(nameof(RegisterCommandHistories));
            OnPropertyChanged(nameof(MotionHistoryText));
            OnPropertyChanged(nameof(RegisterHistoryText));
        }

        /// <summary>
        /// 格式化 Modbus 指令历史记录文本
        /// </summary>
        private static string FormatModbusHistoryText(IEnumerable<CommandHistory> histories)
        {
            if (!histories.Any())
                return "暂无历史记录";

            var lines = new List<string>();

            foreach (var history in histories.Take(50)) // 只显示最近50条记录
            {
                var line = $"{history.DirectionSymbol} {history.HexData}";

                if (history.Status != ModbusCommandStatus.Success)
                {
                    var statusIcon = history.Status switch
                    {
                        ModbusCommandStatus.Failed => " [失败]",
                        ModbusCommandStatus.Timeout => " [超时]",
                        ModbusCommandStatus.Exception => " [异常]",
                        _ => ""
                    };
                    line += statusIcon;

                    if (!string.IsNullOrEmpty(history.ErrorMessage))
                    {
                        line += $" {history.ErrorMessage}";
                    }
                }

                lines.Add(line);
            }

            return string.Join(Environment.NewLine, lines);
        }

        /// <summary>
        /// 初始化测试历史数据
        /// </summary>
        private void InitializeTestHistoryData()
        {
            try
            {
                // 添加一些运动控制指令历史记录（写入单个寄存器）
                var motionSend1 = CommandHistory.CreateSendCommand(1, 0x06, new byte[] { 0x01, 0x06, 0x20, 0x00, 0x00, 0x01, 0x48, 0x0A }, "写入控制命令-正向运行");
                _commandHistoryService.AddCommand(motionSend1);

                var motionReceive1 = CommandHistory.CreateReceiveCommand(1, 0x06, new byte[] { 0x01, 0x06, 0x20, 0x00, 0x00, 0x01, 0x48, 0x0A }, "控制命令响应");
                _commandHistoryService.AddCommand(motionReceive1);

                var motionSend2 = CommandHistory.CreateSendCommand(1, 0x06, new byte[] { 0x01, 0x06, 0x20, 0x01, 0x05, 0xDC, 0x49, 0xF6 }, "写入控制速度-1500");
                _commandHistoryService.AddCommand(motionSend2);

                var motionReceive2 = CommandHistory.CreateReceiveCommand(1, 0x06, new byte[] { 0x01, 0x06, 0x20, 0x01, 0x05, 0xDC, 0x49, 0xF6 }, "速度设置响应");
                _commandHistoryService.AddCommand(motionReceive2);

                // 添加一些寄存器读取指令历史记录
                var readSend1 = CommandHistory.CreateSendCommand(1, 0x03, new byte[] { 0x01, 0x03, 0x20, 0x00, 0x00, 0x0A, 0xC5, 0xCD }, "读取保持寄存器");
                _commandHistoryService.AddCommand(readSend1);

                var readReceive1 = CommandHistory.CreateReceiveCommand(1, 0x03, new byte[] { 0x01, 0x03, 0x14, 0x00, 0x01, 0x05, 0xDC, 0x00, 0x04, 0x00, 0x64, 0x00, 0x64, 0x00, 0x06, 0x00, 0x01, 0x00, 0x02, 0x25, 0x80, 0x7E, 0x9C }, "寄存器读取响应");
                _commandHistoryService.AddCommand(readReceive1);

                // 添加一些写入多个寄存器指令历史记录
                var writeSend1 = CommandHistory.CreateSendCommand(1, 0x10, new byte[] { 0x01, 0x10, 0x20, 0x10, 0x00, 0x03, 0x06, 0x00, 0x64, 0x00, 0xC8, 0x01, 0x2C, 0x9E, 0x15 }, "写入多个寄存器");
                _commandHistoryService.AddCommand(writeSend1);

                var writeReceive1 = CommandHistory.CreateReceiveCommand(1, 0x10, new byte[] { 0x01, 0x10, 0x20, 0x10, 0x00, 0x03, 0x01, 0xC1 }, "写入响应");
                _commandHistoryService.AddCommand(writeReceive1);

                // 添加一个失败的指令
                var errorCommand = CommandHistory.CreateErrorCommand(1, 0x03, "设备连接超时", "读取寄存器失败");
                _commandHistoryService.AddCommand(errorCommand);

                Logger.Info("初始化测试历史数据完成");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "初始化测试历史数据失败");
            }
        }

        /// <summary>
        /// 执行带历史记录的异步操作
        /// </summary>
        private async Task<T> ExecuteWithHistoryAsync<T>(CommandHistory commandHistory, Func<Task<T>> operation)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // 添加到历史记录
                _commandHistoryService.AddCommand(commandHistory);

                // 执行操作
                var result = await operation();

                stopwatch.Stop();

                // 更新执行结果
                var status = EqualityComparer<T>.Default.Equals(result, default(T)) ? ModbusCommandStatus.Failed : ModbusCommandStatus.Success;
                _commandHistoryService.UpdateCommandResult(commandHistory.Id, status, stopwatch.ElapsedMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                // 更新失败结果
                _commandHistoryService.UpdateCommandResult(commandHistory.Id, ModbusCommandStatus.Failed,
                    stopwatch.ElapsedMilliseconds, ex.Message);

                throw;
            }
        }

        /// <summary>
        /// 执行带历史记录的运动控制操作
        /// </summary>
        private async Task ExecuteMotionCommandWithHistoryAsync(string commandName, string description, Func<Task> operation)
        {
            // 创建发送指令历史记录（模拟写入单个寄存器）
            var commandHistory = CommandHistory.CreateSendCommand(GetSlaveAddress(), 0x06, new byte[] { 0x01, 0x06, 0x20, 0x00, 0x00, 0x01 }, description);
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // 添加到历史记录
                _commandHistoryService.AddCommand(commandHistory);

                // 执行操作
                await operation();

                stopwatch.Stop();

                // 更新成功结果
                _commandHistoryService.UpdateCommandResult(commandHistory.Id, ModbusCommandStatus.Success, stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                // 更新失败结果
                _commandHistoryService.UpdateCommandResult(commandHistory.Id, ModbusCommandStatus.Failed,
                    stopwatch.ElapsedMilliseconds, ex.Message);

                throw;
            }
        }

        partial void OnReadStartAddressChanged(int value)
        {
            UpdateReadRegisters();
        }

        partial void OnReadQuantityChanged(int value)
        {
            UpdateReadRegisters();
        }

        partial void OnWriteStartAddressChanged(int value)
        {
            UpdateWriteRegisters();
        }

        partial void OnWriteQuantityChanged(int value)
        {
            UpdateWriteRegisters();
        }

        private void UpdateReadRegisters()
        {
            ReadRegisters.Clear();
            for (int i = 0; i < ReadQuantity; i++)
            {
                ReadRegisters.Add(new RegisterData { AddressDec = ReadStartAddress + i, ValueDec = 0 });
            }
        }

        private void UpdateWriteRegisters()
        {
            WriteRegisters.Clear();
            for (int i = 0; i < WriteQuantity; i++)
            {
                WriteRegisters.Add(new RegisterData { AddressDec = WriteStartAddress + i, ValueDec = 0 });
            }
        }

        #region Commands

        [RelayCommand]
        private async Task JogLeft()
        {
            try
            {
                await ExecuteMotionCommandWithHistoryAsync("左点动", "执行左点动操作", async () =>
                {
                    // TODO: 实现左点动功能
                    await Task.Delay(100); // 模拟操作延时
                    Logger.Info("执行左点动操作");
                });
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "左点动操作失败");
                MessageHelper.Error($"左点动操作失败: {ex.Message}", "操作失败");
            }
        }

        [RelayCommand]
        private async Task JogRight()
        {
            try
            {
                await ExecuteMotionCommandWithHistoryAsync("右点动", "执行右点动操作", async () =>
                {
                    // TODO: 实现右点动功能
                    await Task.Delay(100); // 模拟操作延时
                    Logger.Info("执行右点动操作");
                });
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "右点动操作失败");
                MessageHelper.Error($"右点动操作失败: {ex.Message}", "操作失败");
            }
        }

        [RelayCommand]
        private async Task ConstantSpeedPositive()
        {
            try
            {
                await ExecuteMotionCommandWithHistoryAsync("正向定速", "执行正向定速运行", async () =>
                {
                    // TODO: 实现正向定速运行功能
                    await Task.Delay(100); // 模拟操作延时
                    Logger.Info("执行正向定速运行");
                });
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "正向定速运行失败");
                MessageHelper.Error($"正向定速运行失败: {ex.Message}", "操作失败");
            }
        }

        [RelayCommand]
        private async Task ConstantSpeedNegative()
        {
            try
            {
                await ExecuteMotionCommandWithHistoryAsync("负向定速", "执行负向定速运行", async () =>
                {
                    // TODO: 实现负向定速运行功能
                    await Task.Delay(100); // 模拟操作延时
                    Logger.Info("执行负向定速运行");
                });
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "负向定速运行失败");
                MessageHelper.Error($"负向定速运行失败: {ex.Message}", "操作失败");
            }
        }

        [RelayCommand]
        private async Task Stop()
        {
            try
            {
                await ExecuteMotionCommandWithHistoryAsync("停止", "执行停止操作", async () =>
                {
                    // TODO: 实现停止功能
                    await Task.Delay(50); // 模拟操作延时
                    Logger.Info("执行停止操作");
                });
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "停止操作失败");
                MessageHelper.Error($"停止操作失败: {ex.Message}", "操作失败");
            }
        }

        [RelayCommand]
        private async Task AlarmClear()
        {
            try
            {
                await ExecuteMotionCommandWithHistoryAsync("报警清除", "执行报警清除操作", async () =>
                {
                    // TODO: 实现报警清除功能
                    await Task.Delay(50); // 模拟操作延时
                    Logger.Info("执行报警清除操作");
                });
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "报警清除操作失败");
                MessageHelper.Error($"报警清除操作失败: {ex.Message}", "操作失败");
            }
        }

        [RelayCommand]
        private void ClearHistory()
        {
            try
            {
                // 清除运动控制相关的指令历史（功能码 0x06 的写入单个寄存器指令）
                var motionHistories = _commandHistoryService.CommandHistories
                    .Where(h => h.FunctionCode == 0x06)
                    .ToList();

                if (motionHistories.Any())
                {
                    _commandHistoryService.RemoveCommands(motionHistories);
                    Logger.Info($"清除了 {motionHistories.Count} 条运动控制指令历史");
                    MessageHelper.Success($"已清除 {motionHistories.Count} 条运动控制指令历史", "清除成功");
                }
                else
                {
                    MessageHelper.Info("没有运动控制指令历史需要清除", "提示");
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "清除运动控制指令历史失败");
                MessageHelper.Error($"清除指令历史失败: {ex.Message}", "清除失败");
            }
        }

        [RelayCommand]
        private async Task ReadRegister()
        {
            // 验证所有属性
            ValidateAllProperties();
            if (HasErrors)
            {
                var errors = string.Join("; ", GetErrors().Select(e => e.ErrorMessage ?? e.ToString()));
                Logger.Warn($"参数验证失败: {errors}");
                MessageHelper.Warning($"参数验证失败: {errors}", "参数错误");
                return;
            }

            var modbusHandler = GetModbusHandler();
            if (modbusHandler == null)
            {
                const string errorMsg = "Modbus未连接，请先连接设备";
                Logger.Warn(errorMsg);
                MessageHelper.Warning(errorMsg, "连接错误");
                return;
            }

            // 创建指令历史记录（发送读取指令）
            var commandHistory = CommandHistory.CreateSendCommand(GetSlaveAddress(), 0x03,
                new byte[] { GetSlaveAddress(), 0x03, (byte)(ReadStartAddress >> 8), (byte)(ReadStartAddress & 0xFF),
                           (byte)(ReadQuantity >> 8), (byte)(ReadQuantity & 0xFF) },
                $"读取保持寄存器 地址:0x{ReadStartAddress:X4} 数量:{ReadQuantity}");

            try
            {
                var result = await ExecuteWithHistoryAsync(commandHistory, async () =>
                {
                    Logger.Info($"开始读取寄存器 - 从站地址: {commandHistory.SlaveId}, 起始地址: 0x{ReadStartAddress:X4}, 数量: {ReadQuantity}");

                    // 读取多个寄存器
                    var values = await modbusHandler.ReadHoldingRegistersAsync(
                        GetSlaveAddress(),
                        (ushort)ReadStartAddress,
                        (ushort)ReadQuantity);

                    if (values != null && values.Length > 0)
                    {
                        // 更新ReadRegisters集合中的值
                        for (int i = 0; i < values.Length && i < ReadRegisters.Count; i++)
                        {
                            ReadRegisters[i].ValueDec = values[i];
                            Logger.Debug($"寄存器 0x{ReadRegisters[i].AddressDec:X4} = 0x{values[i]:X4} ({values[i]})");
                        }

                        // 添加接收指令历史记录
                        var receiveHistory = CommandHistory.CreateReceiveCommand(GetSlaveAddress(), 0x03,
                            values.SelectMany(v => new byte[] { (byte)(v >> 8), (byte)(v & 0xFF) }).ToArray(),
                            $"读取响应 数据:{string.Join(", ", values.Select(v => $"0x{v:X4}"))}");
                        _commandHistoryService.AddCommand(receiveHistory);

                        var successMsg = $"成功读取 {values.Length} 个寄存器";
                        Logger.Info(successMsg);
                        MessageHelper.Success(successMsg, "读取成功");

                        return values;
                    }
                    else
                    {
                        const string errorMsg = "读取寄存器失败，请检查连接和地址设置";
                        Logger.Error(errorMsg);
                        MessageHelper.Error(errorMsg, "读取失败");
                        return null;
                    }
                });
            }
            catch (Exception ex)
            {
                var errorMsg = $"读取寄存器时发生错误: {ex.Message}";
                Logger.Error(ex, errorMsg);
                MessageHelper.Error(errorMsg, "读取错误");
            }
        }

        [RelayCommand]
        private async Task WriteRegister()
        {
            // 验证所有属性
            ValidateAllProperties();
            if (HasErrors)
            {
                var errors = string.Join("; ", GetErrors().Select(e => e.ErrorMessage ?? e.ToString()));
                Logger.Warn($"参数验证失败: {errors}");
                MessageHelper.Warning($"参数验证失败: {errors}", "参数错误");
                return;
            }

            var modbusHandler = GetModbusHandler();
            if (modbusHandler == null)
            {
                const string errorMsg = "Modbus未连接，请先连接设备";
                Logger.Warn(errorMsg);
                MessageHelper.Warning(errorMsg, "连接错误");
                return;
            }

            // 准备要写入的值数组
            var values = WriteRegisters.Select(r => (ushort)r.ValueDec).ToArray();

            // 创建指令历史记录（发送写入指令）
            var dataBytes = new List<byte> { GetSlaveAddress(), 0x10,
                (byte)(WriteStartAddress >> 8), (byte)(WriteStartAddress & 0xFF),
                (byte)(values.Length >> 8), (byte)(values.Length & 0xFF),
                (byte)(values.Length * 2) };

            foreach (var value in values)
            {
                dataBytes.Add((byte)(value >> 8));
                dataBytes.Add((byte)(value & 0xFF));
            }

            var commandHistory = CommandHistory.CreateSendCommand(GetSlaveAddress(), 0x10, dataBytes.ToArray(),
                $"写入多个寄存器 地址:0x{WriteStartAddress:X4} 数量:{values.Length}");

            try
            {
                var result = await ExecuteWithHistoryAsync(commandHistory, async () =>
                {
                    Logger.Info($"开始写入寄存器 - 从站地址: {commandHistory.SlaveId}, 起始地址: 0x{WriteStartAddress:X4}, 数量: {values.Length}");

                    // 记录要写入的值
                    for (int i = 0; i < values.Length; i++)
                    {
                        Logger.Debug($"写入寄存器 0x{(WriteStartAddress + i):X4} = 0x{values[i]:X4} ({values[i]})");
                    }

                    // 写入多个寄存器
                    bool success = await modbusHandler.WriteMultipleRegistersAsync(
                        GetSlaveAddress(),
                        (ushort)WriteStartAddress,
                        values);

                    if (success)
                    {
                        var successMsg = $"成功写入 {values.Length} 个寄存器";
                        Logger.Info(successMsg);
                        MessageHelper.Success(successMsg, "写入成功");

                        // 添加接收指令历史记录
                        var receiveHistory = CommandHistory.CreateReceiveCommand(GetSlaveAddress(), 0x10,
                            new byte[] { GetSlaveAddress(), 0x10, (byte)(WriteStartAddress >> 8), (byte)(WriteStartAddress & 0xFF),
                                       (byte)(values.Length >> 8), (byte)(values.Length & 0xFF) },
                            "写入响应");
                        _commandHistoryService.AddCommand(receiveHistory);

                        return true;
                    }
                    else
                    {
                        const string errorMsg = "写入寄存器失败，请检查连接和地址设置";
                        Logger.Error(errorMsg);
                        MessageHelper.Error(errorMsg, "写入失败");
                        return false;
                    }
                });
            }
            catch (Exception ex)
            {
                var errorMsg = $"写入寄存器时发生错误: {ex.Message}";
                Logger.Error(ex, errorMsg);
                MessageHelper.Error(errorMsg, "写入错误");
            }
        }

        [RelayCommand]
        private void ClearRegisterHistory()
        {
            try
            {
                // 清除寄存器读写相关的指令历史（功能码 0x03 和 0x10）
                var registerHistories = _commandHistoryService.CommandHistories
                    .Where(h => h.FunctionCode == 0x03 || h.FunctionCode == 0x10)
                    .ToList();

                if (registerHistories.Any())
                {
                    _commandHistoryService.RemoveCommands(registerHistories);
                    Logger.Info($"清除了 {registerHistories.Count} 条寄存器操作指令历史");
                    MessageHelper.Success($"已清除 {registerHistories.Count} 条寄存器操作指令历史", "清除成功");
                }
                else
                {
                    MessageHelper.Info("没有寄存器操作指令历史需要清除", "提示");
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "清除寄存器操作指令历史失败");
                MessageHelper.Error($"清除指令历史失败: {ex.Message}", "清除失败");
            }
        }

        #endregion
    }
}