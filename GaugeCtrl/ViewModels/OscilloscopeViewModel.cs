using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ScottPlot;
using ScottPlot.WPF;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Win32;
using System.IO;
using GaugeCtrl.Communication;
using GaugeCtrl.Core.Models;
using System.Collections.Concurrent;
using System;
using GaugeCtrl.Helpers;

namespace GaugeCtrl.ViewModels
{
    public partial class OscilloscopeViewModel : ObservableObject
    {
        private readonly Timer _displayTimer;
        private readonly ISerialPortManager _serialPortManager;

        // 高性能数据缓存 - 使用ConcurrentQueue确保线程安全
        private readonly ConcurrentQueue<double> _channel1Buffer = new();
        private readonly ConcurrentQueue<double> _channel2Buffer = new();

        // 显示用的数据列表
        private readonly List<double> _channel1DisplayData = new();
        private readonly List<double> _channel2DisplayData = new();

        // DataLogger用于在线显示
        private ScottPlot.Plottables.DataLogger? _dataLogger1;
        private ScottPlot.Plottables.DataLogger? _dataLogger2;

        // Crosshair十字线
        private ScottPlot.Plottables.Crosshair? _crosshair;

        private bool _isRunning = false;
        private long _sampleCount = 0;

        [ObservableProperty]
        private int _sampleIntervalMicroseconds = 500; 

        /// <summary>
        /// 采样间隔（秒）
        /// </summary>
        public double SampleIntervalSeconds => SampleIntervalMicroseconds / 1_000_000.0;

        [ObservableProperty]
        private WpfPlot? _mainWpfPlot;

        [ObservableProperty]
        private bool _isCrosshairEnabled = false;

        [ObservableProperty]
        private bool _canStart = true;

        [ObservableProperty]
        private bool _canStop = false;

        [ObservableProperty]
        private bool _canClear = false;

        private Plot? _mainPlot;
        private bool _hasData = false;

        public OscilloscopeViewModel(ISerialPortManager? serialPortManager = null)
        {
            _serialPortManager = serialPortManager ?? SerialPortManagerSingleton.Instance;
            _serialPortManager.OscilloscopeDataReceived += OnOscilloscopeDataReceived;

            // 50ms间隔的显示定时器
            _displayTimer = new Timer(UpdateDisplay, null, Timeout.Infinite, Timeout.Infinite);
        }

        partial void OnIsCrosshairEnabledChanged(bool value)
        {
            UpdateCrosshairVisibility();
        }

        private void UpdateCrosshairVisibility()
        {
            if (_crosshair != null)
            {
                // 十字线只在停止状态且启用时才显示
                _crosshair.IsVisible = IsCrosshairEnabled && !_isRunning;
                MainWpfPlot?.Refresh();
            }
        }

        public void InitializePlot(WpfPlot wpfPlot)
        {
            MainWpfPlot = wpfPlot;
            _mainPlot = wpfPlot.Plot;

            // 配置图表
            _mainPlot.Axes.Bottom.Label.Text = "时间 (μs)";
            _mainPlot.Axes.Left.Label.Text = "UINT16 Value";

            // 设置支持中文的字体
            try
            {
                _mainPlot.Axes.Bottom.Label.FontName = "Microsoft YaHei";
                _mainPlot.Axes.Left.Label.FontName = "Microsoft YaHei";
                _mainPlot.Legend.FontName = "Microsoft YaHei";
            }
            catch
            {
                // 如果微软雅黑不可用，尝试其他中文字体
                try
                {
                    _mainPlot.Axes.Bottom.Label.FontName = "SimHei";
                    _mainPlot.Axes.Left.Label.FontName = "SimHei";
                    _mainPlot.Legend.FontName = "SimHei";
                }
                catch
                {
                    // 如果都不可用，使用默认字体
                }
            }
            _mainPlot.Axes.SetLimitsY(0, 65535); // UINT16范围

            // 添加图例
            _mainPlot.Legend.IsVisible = true;
            _mainPlot.Legend.Alignment = Alignment.UpperRight;

            // 添加Crosshair
            _crosshair = _mainPlot.Add.Crosshair(0, 0);
            _crosshair.IsVisible = IsCrosshairEnabled && !_isRunning; // 只在停止状态下显示
            _crosshair.LineColor = Colors.Green;
            _crosshair.LineWidth = 1;
            _crosshair.LinePattern = LinePattern.Dashed;

            // 启用鼠标跟随
            if (MainWpfPlot != null)
            {
                MainWpfPlot.MouseMove += (sender, e) =>
                {
                    if (_crosshair != null && _crosshair.IsVisible && !_isRunning)
                    {
                        var mousePosition = e.GetPosition(MainWpfPlot);
                        var coord = MainWpfPlot.Plot.GetCoordinates((float)mousePosition.X, (float)mousePosition.Y);
                        _crosshair.Position = coord;
                        MainWpfPlot.Refresh();
                    }
                };
            }

            // 初始化DataLogger
            InitializeDataLoggers();
        }

        private void InitializeDataLoggers()
        {
            if (_mainPlot == null) return;

            // 创建DataLogger用于在线显示
            _dataLogger1 = _mainPlot.Add.DataLogger();
            _dataLogger1.Color = Colors.Blue;
            _dataLogger1.LineWidth = 2;
            _dataLogger1.LegendText = "通道1";
            _dataLogger1.ViewJump();

            _dataLogger2 = _mainPlot.Add.DataLogger();
            _dataLogger2.Color = Colors.Red;
            _dataLogger2.LineWidth = 2;
            _dataLogger2.LegendText = "通道2";
            _dataLogger2.ViewJump();
        }

        [RelayCommand]
        private async Task Start()
        {
            if (!_isRunning)
            {
                _isRunning = true;
                _sampleCount = 0;
                _hasData = false;

                // 更新按钮状态
                CanStart = false;
                CanStop = true;
                CanClear = false;

                // 清空缓存
                while (_channel1Buffer.TryDequeue(out _)) { }
                while (_channel2Buffer.TryDequeue(out _)) { }

                // 清空DataLogger
                _dataLogger1?.Clear();
                _dataLogger2?.Clear();

                // 启动显示定时器 - 50ms间隔
                _displayTimer.Change(0, 50);

                // 更新十字线可见性（运行时隐藏）
                UpdateCrosshairVisibility();

                // 如果串口未连接，启动模拟数据用于演示
                if (!_serialPortManager.IsConnected)
                {
                    await StartDataSimulation();
                }
            }
        }

        private Timer? _simulationTimer;

        private async Task StartDataSimulation()
        {
            // 模拟串口数据接收
            await Task.Run(() =>
            {
                var random = new Random();
                _simulationTimer = new Timer(_ =>
                {
                    if (!_isRunning) return;

                    // 模拟串口数据
                    var data = new OscilloscopeData
                    {
                        Channel1Value = (ushort)(32768 + 10000 * Math.Sin(_sampleCount * SampleIntervalSeconds * 2 * Math.PI) + random.Next(-1000, 1000)),
                        Channel2Value = (ushort)(32768 + 8000 * Math.Cos(_sampleCount * SampleIntervalSeconds * 3 * Math.PI) + random.Next(-800, 800))
                    };

                    OnOscilloscopeDataReceived(data);
                }, null, 0, (int)(SampleIntervalMicroseconds / 1000)); // 转换为毫秒
            });
        }

        [RelayCommand]
        private void Stop()
        {
            if (_isRunning)
            {
                _isRunning = false;
                _displayTimer.Change(Timeout.Infinite, Timeout.Infinite);

                // 停止模拟定时器
                _simulationTimer?.Dispose();
                _simulationTimer = null;

                // 更新十字线可见性（停止时显示）
                UpdateCrosshairVisibility();

                // 停止时将缓存数据用Signal填充到Plot
                FillPlotWithSignal();

                // 检查是否有数据
                _hasData = _channel1Buffer.Count > 0 || _channel2Buffer.Count > 0;

                // 更新按钮状态
                CanStart = !_hasData; // 有数据时禁用启动
                CanStop = false;
                CanClear = _hasData; // 有数据时启用清除
            }
        }

        private void FillPlotWithSignal()
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                try
                {
                    // 将缓存数据转换为数组
                    var channel1Array = _channel1Buffer.ToArray();
                    var channel2Array = _channel2Buffer.ToArray();

                    if (channel1Array.Length == 0) return;

                    // 清除DataLogger
                    _mainPlot?.Clear();

                    // 使用Signal填充Plot
                    // 创建时间轴数组，确保x轴显示正确的微秒间隔
                    var timeArray = Enumerable.Range(0, channel1Array.Length)
                        .Select(i => (double)(i * SampleIntervalMicroseconds))
                        .ToArray();

                    var signal1 = _mainPlot?.Add.Scatter(timeArray, channel1Array);
                    if (signal1 != null)
                    {
                        signal1.Color = Colors.Blue;
                        signal1.LineWidth = 2;
                        signal1.LegendText = "通道1";
                    }

                    var signal2 = _mainPlot?.Add.Scatter(timeArray, channel2Array);
                    if (signal2 != null)
                    {
                        signal2.Color = Colors.Red;
                        signal2.LineWidth = 2;
                        signal2.LegendText = "通道2";
                    }

                    // 自动缩放
                    _mainPlot?.Axes.AutoScale();
                    MainWpfPlot?.Refresh();
                }
                catch (Exception ex)
                {
                    MessageHelper.Error("填充图表时发生错误", ex);
                }
            });
        }

        [RelayCommand]
        private void Clear()
        {
            // 清空所有数据
            while (_channel1Buffer.TryDequeue(out _)) { }
            while (_channel2Buffer.TryDequeue(out _)) { }
            _channel1DisplayData.Clear();
            _channel2DisplayData.Clear();
            _sampleCount = 0;
            _hasData = false;

            // 更新按钮状态
            CanStart = true;
            CanStop = false;
            CanClear = false;

            Application.Current.Dispatcher.Invoke(() =>
            {
                _mainPlot?.Clear();
                InitializeDataLoggers(); // 重新初始化DataLogger
                MainWpfPlot?.Refresh();
            });
        }

        [RelayCommand]
        private void SaveData()
        {
            var saveFileDialog = new SaveFileDialog
            {
                Filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*",
                DefaultExt = "csv"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    var channel1Array = _channel1Buffer.ToArray();
                    var channel2Array = _channel2Buffer.ToArray();

                    var lines = new List<string> { "Sample,Time(μs),Channel1,Channel2" };
                    var minLength = Math.Min(channel1Array.Length, channel2Array.Length);

                    for (int i = 0; i < minLength; i++)
                    {
                        var timeUs = i * SampleIntervalMicroseconds;
                        lines.Add($"{i},{timeUs:F1},{channel1Array[i]:F0},{channel2Array[i]:F0}");
                    }

                    File.WriteAllLines(saveFileDialog.FileName, lines);
                    MessageHelper.Success($"数据已保存成功！共{minLength}个采样点", "保存完成");
                }
                catch (Exception ex)
                {
                    MessageHelper.Error("保存失败", ex);
                }
            }
        }

        [RelayCommand]
        private void ExportImage()
        {
            var saveFileDialog = new SaveFileDialog
            {
                Filter = "PNG files (*.png)|*.png|All files (*.*)|*.*",
                DefaultExt = "png"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    _mainPlot?.SavePng(saveFileDialog.FileName, 1200, 800);
                    MessageHelper.Success("图像已导出成功！", "导出完成");
                }
                catch (Exception ex)
                {
                    MessageHelper.Error("导出失败", ex);
                }
            }
        }

        [RelayCommand]
        private void AutoScale()
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                try
                {
                    _mainPlot?.Axes.AutoScale();
                    MainWpfPlot?.Refresh();
                }
                catch (Exception ex)
                {
                    MessageHelper.Error("自动缩放失败", ex);
                }
            });
        }

        /// <summary>
        /// 串口数据接收事件处理
        /// 高性能缓存数据到队列中
        /// </summary>
        private void OnOscilloscopeDataReceived(OscilloscopeData data)
        {
            if (!_isRunning) return;

            // 高性能缓存 - 直接入队，避免锁定
            _channel1Buffer.Enqueue(data.Channel1Value);
            _channel2Buffer.Enqueue(data.Channel2Value);

            _channel1DisplayData.Add(data.Channel1Value);
            _channel2DisplayData.Add(data.Channel2Value);

            Interlocked.Increment(ref _sampleCount);
        }

        /// <summary>
        /// 50ms间隔的显示更新
        /// 使用DataLogger进行在线显示
        /// </summary>
        private void UpdateDisplay(object? state)
        {
            if (!_isRunning || _dataLogger1 == null || _dataLogger2 == null)
                return;

            Application.Current.Dispatcher.Invoke(() =>
            {
                try
                {
                    // 从缓存队列中获取最新的数据点进行显示
                    var bufferArray1 = _channel1Buffer.ToArray();
                    var bufferArray2 = _channel2Buffer.ToArray();

                    if (bufferArray1.Length > 0 && bufferArray2.Length > 0)
                    {
                        var minLength = Math.Min(bufferArray1.Length, bufferArray2.Length);

                        // 添加最新的数据点到DataLogger
                        var latestIndex = minLength - 1;
                        if (latestIndex >= 0)
                        {
                            var timeUs = latestIndex * SampleIntervalMicroseconds;
                            _dataLogger1.Add(timeUs, bufferArray1[latestIndex]);
                            _dataLogger2.Add(timeUs, bufferArray2[latestIndex]);
                        }
                    }

                    // 限制DataLogger显示的点数，避免性能问题
                    if (_sampleCount > 10000)
                    {
                        _dataLogger1.Clear();
                        _dataLogger2.Clear();
                    }

                    // 自动缩放X轴
                    if (_sampleCount > 0)
                    {
                        var latestTime = (_sampleCount - 1) * SampleIntervalMicroseconds;
                        var windowSize = 50000; // 显示最近50ms的数据
                        _mainPlot?.Axes.SetLimitsX(Math.Max(0, latestTime - windowSize), latestTime + windowSize * 0.1);
                    }

                    MainWpfPlot?.Refresh();
                }
                catch
                {
                    // 忽略显示更新过程中的异常
                }
            });
        }

        public void Dispose()
        {
            _isRunning = false;
            _displayTimer?.Dispose();
            _serialPortManager?.Dispose();
        }
    }
}