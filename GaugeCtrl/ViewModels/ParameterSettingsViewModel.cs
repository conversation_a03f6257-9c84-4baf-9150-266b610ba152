using CommunityToolkit.Mvvm.ComponentModel;
using GaugeCtrl.Core.Models;
using GaugeCtrl.Communication;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using GaugeCtrl.Communication.Interfaces;
using GaugeCtrl.Core.Expression;

namespace GaugeCtrl.ViewModels
{
    public partial class ParameterSettingsViewModel : ObservableObject
    {
        private readonly ISerialPortManager? _serialPortManager;

        /// <summary>
        /// 参数修改状态变更事件
        /// </summary>
        public event Action<bool>? ParameterModificationChanged;

        public BindingList<Parameter> IoParameters { get; private set; }

        public BindingList<Parameter> MotorParameters { get; private set; }

        public BindingList<Parameter> ProtectionParameters { get; private set; }

        public BindingList<Parameter> ControlParameters { get; private set; }

        public BindingList<Parameter> MotionParameters { get; private set; }

        public BindingList<Parameter> MonitoringParameters { get; private set; }

        [ObservableProperty] private bool _hasValidationErrors;

        [ObservableProperty] private AxisType _selectedAxis = AxisType.Axis0;

        public ParameterSettingsViewModel(ISerialPortManager? serialPortManager = null)
        {
            _serialPortManager = serialPortManager ?? SerialPortManagerSingleton.Instance;

            InitializeIOParameters();
            InitializeMotorParameters();
            InitializeProtectionParameters();
            InitializeControlParameters();
            InitializeMotionParameters();
            InitializeMonitoringParameters();
            UpdateRegisterAddresses();
            SubscribeToParameterChanges();
        }

        partial void OnSelectedAxisChanged(AxisType value)
        {
            UpdateRegisterAddresses();
        }

        private void UpdateRegisterAddresses()
        {
            ushort baseAddress = SelectedAxis == AxisType.Axis1 ? (ushort)0x1500 : (ushort)0x0000;
            ushort interval = 2;

            // 应用基础地址偏移
            for (int i = 0; i < MotorParameters.Count; i++)
            {
                if (i == 0)
                {
                    MotorParameters[i].RegisterAddress = baseAddress;
                }
                else
                {
                    MotorParameters[i].RegisterAddress = (ushort)(MotorParameters[i - 1].RegisterAddress + interval);
                }
            }

            baseAddress = MotorParameters.Last().RegisterAddress;
            foreach (var param in ControlParameters)
            {
                baseAddress += interval;
                param.RegisterAddress = baseAddress;
            }

            foreach (var param in ProtectionParameters)
            {
                baseAddress += interval;
                param.RegisterAddress = baseAddress;
            }

            foreach (var param in MotionParameters)
            {
                baseAddress += interval;
                param.RegisterAddress = baseAddress;
            }

            // 重新订阅事件
            SubscribeToParameterChanges();
        }

        /// <summary>
        /// 获取Modbus协议处理器用于参数读写
        /// </summary>
        public IModbusProtocolHandler? GetModbusHandler()
        {
            return _serialPortManager?.GetModbusHandler();
        }

        private void SubscribeToParameterChanges()
        {
            // 先取消订阅，避免重复订阅
            UnsubscribeFromParameterChanges();
            IoParameters.ListChanged += ParamItems_ListChanged;
            MotorParameters.ListChanged += ParamItems_ListChanged;
            ProtectionParameters.ListChanged += ParamItems_ListChanged;
            ControlParameters.ListChanged += ParamItems_ListChanged;
            MotionParameters.ListChanged += ParamItems_ListChanged;
            MonitoringParameters.ListChanged += ParamItems_ListChanged;
        }

        private void ParamItems_ListChanged(object sender, ListChangedEventArgs e)
        {
            if (e.ListChangedType == ListChangedType.ItemChanged)
            {
                var item = sender as BindingList<Parameter>;
                var param = item?[e.NewIndex];
                switch (e.PropertyDescriptor?.Name)
                {
                    case "Value":
                        CheckValidationErrors();
                        break;
                    case "IsModified":
                        CheckParameterModificationStatus();
                        break;
                }
                // ObjectUtil<ushort>.SetPropertyValue(obj, param.Name, Convert.ToUInt16(param.Value));
            }
        }

        private void UnsubscribeFromParameterChanges()
        {
            IoParameters.ListChanged -= ParamItems_ListChanged;
            MotorParameters.ListChanged -= ParamItems_ListChanged;
            ProtectionParameters.ListChanged -= ParamItems_ListChanged;
            ControlParameters.ListChanged -= ParamItems_ListChanged;
            MotionParameters.ListChanged -= ParamItems_ListChanged;
            MonitoringParameters.ListChanged -= ParamItems_ListChanged;
        }

        private void OnParameterPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == "Value")
            {
                CheckValidationErrors();
            }
            else if (e.PropertyName == "IsModified")
            {
                CheckParameterModificationStatus();
            }
        }

        /// <summary>
        /// 检查参数修改状态
        /// </summary>
        public void CheckParameterModificationStatus()
        {
            var hasModified = MotorParameters.Any(p => p.IsModified) ||
                              ProtectionParameters.Any(p => p.IsModified) ||
                              ControlParameters.Any(p => p.IsModified) ||
                              MotionParameters.Any(p => p.IsModified);

            ParameterModificationChanged?.Invoke(hasModified);
        }

        private void CheckValidationErrors()
        {
            var allParameters = IoParameters.Cast<IDataErrorInfo>()
                .Concat(MotorParameters.Cast<IDataErrorInfo>())
                .Concat(ProtectionParameters.Cast<IDataErrorInfo>())
                .Concat(ControlParameters.Cast<IDataErrorInfo>())
                .Concat(MotionParameters.Cast<IDataErrorInfo>())
                .Concat(MonitoringParameters.Cast<IDataErrorInfo>());

            HasValidationErrors = allParameters.Any(p => !string.IsNullOrEmpty(p["Value"]));

            UpdateParameterEnabledStates();
        }

        private void UpdateParameterEnabledStates()
        {
            // 找到有验证错误的参数
            var parametersWithErrors = new List<object>();

            foreach (var param in IoParameters)
            {
                var errorInfo = param as IDataErrorInfo;
                if (!string.IsNullOrEmpty(errorInfo["Value"]))
                {
                    parametersWithErrors.Add(param);
                }
            }

            foreach (var param in MotorParameters)
            {
                var errorInfo = param as IDataErrorInfo;
                if (!string.IsNullOrEmpty(errorInfo["Value"]))
                {
                    parametersWithErrors.Add(param);
                }
            }

            foreach (var param in ProtectionParameters)
            {
                var errorInfo = param as IDataErrorInfo;
                if (!string.IsNullOrEmpty(errorInfo["Value"]))
                {
                    parametersWithErrors.Add(param);
                }
            }

            foreach (var param in ControlParameters)
            {
                var errorInfo = param as IDataErrorInfo;
                if (!string.IsNullOrEmpty(errorInfo["Value"]))
                {
                    parametersWithErrors.Add(param);
                }
            }

            foreach (var param in MotionParameters)
            {
                var errorInfo = param as IDataErrorInfo;
                if (!string.IsNullOrEmpty(errorInfo["Value"]))
                {
                    parametersWithErrors.Add(param);
                }
            }

            foreach (var param in MonitoringParameters)
            {
                var errorInfo = param as IDataErrorInfo;
                if (!string.IsNullOrEmpty(errorInfo["Value"]))
                {
                    parametersWithErrors.Add(param);
                }
            }

            // 如果有验证错误，禁用其他所有参数
            bool hasErrors = parametersWithErrors.Count > 0;

            foreach (var param in IoParameters)
            {
                param.IsEnabled = !hasErrors || parametersWithErrors.Contains(param);
            }

            foreach (var param in MotorParameters)
            {
                param.IsEnabled = !hasErrors || parametersWithErrors.Contains(param);
            }

            foreach (var param in ProtectionParameters)
            {
                param.IsEnabled = !hasErrors || parametersWithErrors.Contains(param);
            }

            foreach (var param in ControlParameters)
            {
                param.IsEnabled = !hasErrors || parametersWithErrors.Contains(param);
            }

            foreach (var param in MotionParameters)
            {
                param.IsEnabled = !hasErrors || parametersWithErrors.Contains(param);
            }

            foreach (var param in MonitoringParameters)
            {
                param.IsEnabled = !hasErrors || parametersWithErrors.Contains(param);
            }
        }

        private void InitializeIOParameters()
        {
            IoParameters =
            [
                new Parameter("数字输入1", "使能", ParameterCategory.IO, "", null, null, ParameterType.Enum,
                    new List<object> { "使能", "禁用", "无功能" }),
                new Parameter("数字输入2", "运行", ParameterCategory.IO, "", null, null, ParameterType.Enum,
                    new List<object> { "运行", "停止", "无功能" }),
                new Parameter("数字输入3", "方向", ParameterCategory.IO, "", null, null, ParameterType.Enum,
                    new List<object> { "方向", "无功能" }),
                new Parameter("数字输入4", "复位", ParameterCategory.IO, "", null, null, ParameterType.Enum,
                    new List<object> { "复位", "无功能" }),
                new Parameter("数字输出1", "就绪", ParameterCategory.IO, "", null, null, ParameterType.Enum,
                    new List<object> { "就绪", "故障", "运行中", "无功能" }),
                new Parameter("数字输出2", "故障", ParameterCategory.IO, "", null, null, ParameterType.Enum,
                    new List<object> { "故障", "就绪", "运行中", "无功能" }),
                new Parameter("模拟输入1", "速度指令", ParameterCategory.IO, "", null, null, ParameterType.Enum,
                    new List<object> { "速度指令", "转矩指令", "无功能" }),
                new Parameter("模拟输入2", "转矩指令", ParameterCategory.IO, "", null, null, ParameterType.Enum,
                    new List<object> { "转矩指令", "速度指令", "无功能" })
            ];
        }

        private void InitializeMotorParameters()
        {
            MotorParameters =
            [
                new Parameter("线电阻", 0, ParameterCategory.Motor, "mΩ", 0, 65535, ParameterType.Numeric, null, 0x0000),
                new Parameter("线电感", 0, ParameterCategory.Motor, "μH", 0, 65535, ParameterType.Numeric, null, 0x0002),
                new Parameter("电机额定电流", 0, ParameterCategory.Motor, "mA", 0, 65535, ParameterType.Numeric, null,
                    0x0004),
                new Parameter("电机极对数", 0, ParameterCategory.Motor, "", 0, 65535, ParameterType.Numeric, null, 0x0006),
                new Parameter("磁极相位角", 0, ParameterCategory.Motor, "°", 0, 65535, ParameterType.Numeric, null, 0x0008)
            ];
        }

        private void InitializeProtectionParameters()
        {
            ProtectionParameters = new BindingList<Parameter>
            {
                new Parameter("瞬时过流阈值", 0, ParameterCategory.Protection, "mA", 0, 65535, ParameterType.Numeric, null,
                    0x0014),
                new Parameter("堵转电流阈值", 0, ParameterCategory.Protection, "mA", 0, 65535, ParameterType.Numeric, null,
                    0x0016),
                new Parameter("堵转判定时间", 0, ParameterCategory.Protection, "ms", 0, 65535, ParameterType.Numeric, null,
                    0x0018),
                new Parameter("堵转速度阈值", 0, ParameterCategory.Protection, "rpm", 0, 65535, ParameterType.Numeric, null,
                    0x001A),
                new Parameter("直流母线过压阈值", 0, ParameterCategory.Protection, "mV", 0, 65535, ParameterType.Numeric, null,
                    0x001C),
                new Parameter("直流母线欠压阈值", 0, ParameterCategory.Protection, "mV", 0, 65535, ParameterType.Numeric, null,
                    0x001E),
                new Parameter("电机热时间常数", 0, ParameterCategory.Protection, "s", 0, 65535, ParameterType.Numeric, null,
                    0x0020),
                new Parameter("电机温升系数", 0, ParameterCategory.Protection, "", 0, 65535, ParameterType.Numeric, null,
                    0x0022),
                new Parameter("电机散热系数", 0, ParameterCategory.Protection, "", 0, 65535, ParameterType.Numeric, null,
                    0x0024),
                new Parameter("驱动器热时间常数", 0, ParameterCategory.Protection, "s", 0, 65535, ParameterType.Numeric, null,
                    0x0026),
                new Parameter("驱动器温升系数", 0, ParameterCategory.Protection, "", 0, 65535, ParameterType.Numeric, null,
                    0x0028),
                new Parameter("驱动器散热系数", 0, ParameterCategory.Protection, "", 0, 65535, ParameterType.Numeric, null,
                    0x002A)
            };
        }

        private void InitializeControlParameters()
        {
            ControlParameters =
            [
                new Parameter("电流环比例系数", 0, ParameterCategory.Control, "", 0, 65535, ParameterType.Numeric, null,
                    0x000A),
                new Parameter("电流环积分系数", 0, ParameterCategory.Control, "", 0, 65535, ParameterType.Numeric, null,
                    0x000C),
                new Parameter("速度环比例系数", 0, ParameterCategory.Control, "", 0, 65535, ParameterType.Numeric, null,
                    0x000E),
                new Parameter("速度环积分系数", 0, ParameterCategory.Control, "", 0, 65535, ParameterType.Numeric, null,
                    0x0010),
                new Parameter("速度环输出限幅", 0, ParameterCategory.Control, "", 0, 65535, ParameterType.Numeric, null,
                    0x0012)
            ];
        }

        private void InitializeMotionParameters()
        {
            MotionParameters =
            [
                new Parameter("最大速度", 0, ParameterCategory.Motion, "rpm", 0, 65535, ParameterType.Numeric, null,
                    0x002C),
                new Parameter("加速时间", 0, ParameterCategory.Motion, "ms", 0, 65535, ParameterType.Numeric, null, 0x002E),
                new Parameter("减速时间", 0, ParameterCategory.Motion, "ms", 0, 65535, ParameterType.Numeric, null, 0x0030),
                new Parameter("加速度", 0, ParameterCategory.Motion, "rpm/s", 0, 65535, ParameterType.Numeric, null,
                    0x0032),
                new Parameter("减速度", 0, ParameterCategory.Motion, "rpm/s", 0, 65535, ParameterType.Numeric, null,
                    0x0034)
            ];
        }

        private void InitializeMonitoringParameters()
        {
            MonitoringParameters =
            [
                new Parameter("实时速度", 0, ParameterCategory.Monitoring, "Rpm", null, null, ParameterType.Numeric),
                new Parameter("实时位置", 0, ParameterCategory.Monitoring, "脉冲", null, null, ParameterType.Numeric),
                new Parameter("实时电流", 0.0, ParameterCategory.Monitoring, "A", null, null, ParameterType.Numeric),
                new Parameter("母线电压", 24.0, ParameterCategory.Monitoring, "V", null, null, ParameterType.Numeric),
                new Parameter("驱动器温度", 25.0, ParameterCategory.Monitoring, "℃", null, null, ParameterType.Numeric),
                new Parameter("运行状态", "停止", ParameterCategory.Monitoring, "", null, null, ParameterType.Enum,
                    new List<object> { "停止", "运行", "故障", "就绪" }),
                new Parameter("故障代码", 0, ParameterCategory.Monitoring, "", null, null, ParameterType.Numeric)
            ];
        }

        /// <summary>
        /// 测试方法：手动更新参数值以验证绑定
        /// </summary>
        public void TestParameterValueUpdate()
        {
            System.Diagnostics.Debug.WriteLine("[ParameterSettingsViewModel] Testing parameter value updates...");

            if (MotorParameters?.Count > 0)
            {
                var firstParam = MotorParameters[0];
                System.Diagnostics.Debug.WriteLine($"[Test] Before update: {firstParam.Name} = {firstParam.Value}");

                // 尝试更新值
                firstParam.Value = 999;

                System.Diagnostics.Debug.WriteLine(
                    $"[Test] After update: {firstParam.Name} = {firstParam.Value}, IsModified = {firstParam.IsModified}");
            }
        }
    }
}