using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Win32;
using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;

namespace GaugeCtrl.ViewModels
{
    public partial class FirmwareUpgradeViewModel : ObservableObject
    {
        [ObservableProperty]
        private string _selectedFileName = "";

        [ObservableProperty]
        private string _selectedFileSize = "";

        [ObservableProperty]
        private string _selectedFilePath = "";

        [ObservableProperty]
        private double _upgradeProgress = 0;

        [ObservableProperty]
        private string _upgradeProgressText = "0 %";

        [ObservableProperty]
        private bool _isUpgrading = false;

        [ObservableProperty]
        private bool _canStartUpgrade = false;

        public FirmwareUpgradeViewModel()
        {
            
        }

        [RelayCommand]
        private void SelectFile()
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "选择固件文件",
                    Filter = "固件文件 (*.bin)|*.bin|所有文件 (*.*)|*.*",
                    FilterIndex = 1,
                    RestoreDirectory = true
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    SelectedFilePath = openFileDialog.FileName;
                    SelectedFileName = $"文件名称: {Path.GetFileName(SelectedFilePath)}";
                    
                    var fileInfo = new FileInfo(SelectedFilePath);
                    var fileSizeKB = Math.Round(fileInfo.Length / 1024.0, 2);
                    SelectedFileSize = $"文件大小: {fileSizeKB} KB";
                    
                    CanStartUpgrade = !string.IsNullOrEmpty(SelectedFilePath) && !IsUpgrading;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择文件时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand(CanExecute = nameof(CanExecuteStartUpgrade))]
        private async Task StartUpgrade()
        {
            if (string.IsNullOrEmpty(SelectedFilePath))
            {
                MessageBox.Show("请先选择固件文件", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show(
                "确定要开始固件升级吗？\n\n注意：\n• 升级过程中请勿断电\n• 升级完成后需要重新配置参数", 
                "确认升级", 
                MessageBoxButton.YesNo, 
                MessageBoxImage.Question);

            if (result != MessageBoxResult.Yes)
                return;

            try
            {
                IsUpgrading = true;
                CanStartUpgrade = false;
                
                // 模拟固件升级过程
                await SimulateUpgradeProcess();
                
                MessageBox.Show("固件升级完成！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"固件升级失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsUpgrading = false;
                CanStartUpgrade = !string.IsNullOrEmpty(SelectedFilePath);
                UpgradeProgress = 0;
                UpgradeProgressText = "0 %";
            }
        }

        private bool CanExecuteStartUpgrade()
        {
            return CanStartUpgrade && !IsUpgrading;
        }

        private async Task SimulateUpgradeProcess()
        {
            // 模拟固件升级过程，分为多个阶段
            var stages = new[]
            {
                ("准备升级...", 10),
                ("擦除固件...", 25),
                ("写入固件...", 80),
                ("验证固件...", 95),
                ("升级完成", 100)
            };

            foreach (var (message, targetProgress) in stages)
            {
                // 逐步增加进度
                while (UpgradeProgress < targetProgress)
                {
                    await Task.Delay(50); // 模拟升级时间
                    UpgradeProgress += 1;
                    UpgradeProgressText = $"{UpgradeProgress:F0} %";
                }
                
                // 在某些阶段稍作停留
                if (targetProgress < 100)
                {
                    await Task.Delay(200);
                }
            }
        }

        partial void OnIsUpgradingChanged(bool value)
        {
            StartUpgradeCommand.NotifyCanExecuteChanged();
        }

        partial void OnSelectedFilePathChanged(string value)
        {
            CanStartUpgrade = !string.IsNullOrEmpty(value) && !IsUpgrading;
            StartUpgradeCommand.NotifyCanExecuteChanged();
        }
    }
}