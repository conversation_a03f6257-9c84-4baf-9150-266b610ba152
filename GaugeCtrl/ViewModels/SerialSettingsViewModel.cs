using System.Collections.ObjectModel;
using System.IO;
using System.IO.Ports;
using System.Text.Json;
using System.Text.Json.Serialization;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HandyControl.Controls;
using GaugeCtrl.Communication;
using GaugeCtrl.Communication.Models;
using GaugeCtrl.Core.Models;
using GaugeCtrl.Helpers;

namespace GaugeCtrl.ViewModels
{
    public partial class SerialSettingsViewModel : ObservableObject
    {
        private readonly ISerialPortManager _serialPortManager;
        private readonly string _settingsDirectory;
        private readonly string _configFilePath;
        private Func<bool>? _getIsMasterNodeEnabled;

        public event Action? ConfigurationConfirmed;
        public event Action? ConfigurationCancelled;
        public event Action<List<byte>>? SlaveSearchCompleted;
        public event Action? HandshakeCompleted;

        [ObservableProperty]
        private ObservableCollection<string> _availablePorts = new();

        [ObservableProperty]
        private ObservableCollection<int> _baudRates = new() { 9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600 };

        [ObservableProperty]
        private ObservableCollection<int> _dataBits = new() { 5, 6, 7, 8 };

        [ObservableProperty]
        private ObservableCollection<Parity> _parityOptions = new()
        {
            Parity.None,
            Parity.Odd,
            Parity.Even,
            Parity.Mark,
            Parity.Space
        };

        [ObservableProperty]
        private ObservableCollection<StopBits> _stopBitsOptions = new()
        {
            StopBits.One,
            StopBits.OnePointFive,
            StopBits.Two
        };

        [ObservableProperty]
        private string _selectedPortName = string.Empty;

        [ObservableProperty]
        private int _selectedBaudRate = 115200;

        [ObservableProperty]
        private int _selectedDataBits = 8;

        [ObservableProperty]
        private Parity _selectedParity = Parity.None;

        [ObservableProperty]
        private StopBits _selectedStopBits = StopBits.One;

        [ObservableProperty]
        private bool _isConnected = false;

        [ObservableProperty]
        private bool _isSearching = false;

        [ObservableProperty]
        private string _searchResult = string.Empty;

        [ObservableProperty]
        private bool _isMasterNodeEnabled = false;

        public bool IsConfigured { get; private set; }

        public SerialSettingsViewModel(ISerialPortManager? serialPortManager = null)
        {
            _serialPortManager = serialPortManager ?? SerialPortManagerSingleton.Instance;

            // 订阅连接状态变化事件
            _serialPortManager.ConnectionStatusChanged += OnConnectionStatusChanged;

            // 初始化配置文件路径
            _settingsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "settings");
            _configFilePath = Path.Combine(_settingsDirectory, "serial_config.json");

            // 确保settings目录存在
            if (!Directory.Exists(_settingsDirectory))
            {
                Directory.CreateDirectory(_settingsDirectory);
            }

            LoadConfigurationFromFile();
            RefreshPorts();

            // 初始化连接状态
            IsConnected = _serialPortManager.IsConnected;
        }

        /// <summary>
        /// 设置获取主节点启用状态的委托
        /// </summary>
        /// <param name="getIsMasterNodeEnabled">获取子节点启用状态的委托</param>
        public void SetMasterNodeEnabledGetter(Func<bool> getIsMasterNodeEnabled)
        {
            _getIsMasterNodeEnabled = getIsMasterNodeEnabled;
            // 初始化时同步状态
            IsMasterNodeEnabled = getIsMasterNodeEnabled?.Invoke() ?? false;
        }

        /// <summary>
        /// 主节点启用状态变更时的处理
        /// </summary>
        partial void OnIsMasterNodeEnabledChanged(bool value)
        {
            // 通知主窗口ViewModel状态变更
            MasterNodeEnabledChanged?.Invoke(value);
        }

        /// <summary>
        /// 主节点启用状态变更事件
        /// </summary>
        public event Action<bool>? MasterNodeEnabledChanged;

        /// <summary>
        /// 同步主节点启用状态（从外部调用，不触发事件）
        /// </summary>
        /// <param name="isEnabled">主节点是否启用</param>
        public void SyncMasterNodeEnabled(bool isEnabled)
        {
            // 直接设置属性，不触发事件避免循环调用
            if (_isMasterNodeEnabled != isEnabled)
            {
                _isMasterNodeEnabled = isEnabled;
                OnPropertyChanged(nameof(IsMasterNodeEnabled));
            }
        }

        private void LoadConfigurationFromFile()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    var jsonString = File.ReadAllText(_configFilePath);
                    var options = new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        Converters = { new JsonStringEnumConverter() }
                    };
                    var config = JsonSerializer.Deserialize<SerialPortConfig>(jsonString, options);

                    if (config != null)
                    {
                        SelectedPortName = config.PortName;
                        SelectedBaudRate = config.BaudRate;
                        SelectedDataBits = config.DataBits;
                        SelectedParity = config.Parity;
                        SelectedStopBits = config.StopBits;
                    }
                }
                else
                {
                    // 使用默认配置
                    SelectedPortName = "COM1";
                    SelectedBaudRate = 115200;
                    SelectedDataBits = 8;
                    SelectedParity = Parity.None;
                    SelectedStopBits = StopBits.One;
                }
            }
            catch (Exception ex)
            {
                MessageHelper.Error("加载配置文件失败", ex);
                // 使用默认配置
                SelectedPortName = "COM1";
                SelectedBaudRate = 115200;
                SelectedDataBits = 8;
                SelectedParity = Parity.None;
                SelectedStopBits = StopBits.One;
            }
        }

        [RelayCommand]
        private void RefreshPorts()
        {
            try
            {
                var ports = _serialPortManager.GetAvailablePorts();
                AvailablePorts.Clear();

                foreach (var port in ports)
                {
                    AvailablePorts.Add(port);
                }

                // 如果当前选择的端口不在列表中，选择第一个可用端口
                if (!string.IsNullOrEmpty(SelectedPortName) && !AvailablePorts.Contains(SelectedPortName))
                {
                    SelectedPortName = AvailablePorts.FirstOrDefault() ?? string.Empty;
                }
                else if (string.IsNullOrEmpty(SelectedPortName) && AvailablePorts.Count > 0)
                {
                    SelectedPortName = AvailablePorts.First();
                }
            }
            catch (Exception ex)
            {
                MessageHelper.Error("刷新串口列表失败", ex);
            }
        }

        [RelayCommand]
        private async Task Connect()
        {
            if (ValidateInput())
            {
                var config = new SerialPortConfig
                {
                    PortName = SelectedPortName,
                    BaudRate = SelectedBaudRate,
                    DataBits = SelectedDataBits,
                    Parity = SelectedParity,
                    StopBits = SelectedStopBits,
                    SlaveId = 1 // 默认从站地址，实际使用时会被 ModbusProtocolHandler 的 CurrentSlaveId 覆盖
                };

                // 根据应用场景选择合适的连接方式
                // 串口设置界面通常用于参数配置，使用Modbus协议
                var modbusConfig = new ModbusConfig
                {
                    PortName = config.PortName,
                    BaudRate = config.BaudRate,
                    DataBits = config.DataBits,
                    Parity = config.Parity,
                    StopBits = config.StopBits,
                    SlaveId = config.SlaveId,
                    Timeout = 1000
                };
                var success = await _serialPortManager.ConnectByViewAsync("SerialSettings", null, modbusConfig);
                if (success)
                {
                    SaveConfiguration();
                    MessageHelper.Info("串口连接成功。", "连接成功");

                    // 连接成功后，根据子节点状态执行不同操作
                    var isMasterNodeEnabled = _getIsMasterNodeEnabled?.Invoke() ?? false;
                    if (isMasterNodeEnabled)
                    {
                        // 主节点启用时，自动搜索从站
                        await SearchSlaves();
                    }
                    else
                    {
                        // 主节点未启用时，向从站01寄存器FFFF发送请求用于握手
                        await PerformHandshake();
                    }
                }
                else
                {
                    MessageHelper.Error($"串口 {SelectedPortName} 连接失败！", "连接失败");
                }
            }
        }

        [RelayCommand]
        private async Task Disconnect()
        {
            await _serialPortManager.DisconnectAsync();
            MessageHelper.Warning("串口连接已断开。", "连接断开");
        }
        
        private async Task SearchSlaves()
        {
            IsSearching = true;
            SearchResult = "正在搜索从站...";

            try
            {
                var foundSlaves = new List<byte>();

                // 获取Modbus处理器
                var modbusHandler = _serialPortManager.GetModbusHandler();
                if (modbusHandler == null)
                {
                    SearchResult = "无法获取Modbus处理器";
                    await _serialPortManager.DisconnectAsync();
                    return;
                }

                // 搜索从站地址，4个子节点
                const byte maxSlaveId = 4;
                for (byte slaveId = 1; slaveId <= maxSlaveId; slaveId++)
                {
                    try
                    {
                        // 尝试读取从站地址寄存器 (0x2008)
                        var result = await modbusHandler.ReadHoldingRegisterAsync(slaveId, 0xFFFF);
                        if (result.HasValue)
                        {
                            foundSlaves.Add(slaveId);
                        }
                    }
                    catch
                    {
                        // 忽略读取失败的从站
                    }

                    // 更新搜索进度
                    SearchResult = $"正在搜索从站... ({slaveId}/{maxSlaveId})";

                    // 添加小延时避免过快的请求
                    await Task.Delay(50);
                }
                
                // 显示搜索结果
                if (foundSlaves.Count > 0)
                {
                    var slaveList = string.Join(", ", foundSlaves);
                    SearchResult = $"找到从站: {slaveList}";

                    MessageHelper.Info($"搜索完成，找到 {foundSlaves.Count} 个从站: {slaveList}\n请在主界面侧边栏选择要使用的从站地址。", "搜索结果");
                }
                else
                {
                    SearchResult = "未找到任何从站";
                    MessageHelper.Warning("未找到任何从站设备，请检查连接和设备状态。", "搜索结果");
                }

                // 触发搜索完成事件，通知主界面更新从站地址选项
                SlaveSearchCompleted?.Invoke(foundSlaves);
            }
            catch (Exception ex)
            {
                SearchResult = $"搜索失败: {ex.Message}";
                MessageHelper.Error($"搜索从站时发生错误: {ex.Message}", "搜索错误");
            }
            finally
            {
                IsSearching = false;
            }
        }

        /// <summary>
        /// 执行握手操作（向从站01寄存器FFFF发送请求）
        /// </summary>
        private async Task PerformHandshake()
        {
            try
            {
                var modbusHandler = _serialPortManager.GetModbusHandler();
                if (modbusHandler != null)
                {
                    // 向从站01寄存器FFFF发送读取请求用于握手
                    var result = await modbusHandler.ReadHoldingRegisterAsync(1, 0xFFFF);
                    if (result.HasValue)
                    {
                        MessageHelper.Info("握手成功，设备响应正常。", "握手结果");
                        // 触发握手完成事件，用于自动下载参数
                        HandshakeCompleted?.Invoke();
                    }
                    else
                    {
                        MessageHelper.Warning("握手失败，设备无响应。", "握手结果");
                    }
                }
            }
            catch (Exception ex)
            {
                MessageHelper.Warning($"握手过程中发生错误: {ex.Message}", "握手错误");
            }
        }

        private void OnConnectionStatusChanged(bool isConnected)
        {
            IsConnected = isConnected;
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrEmpty(SelectedPortName))
            {
                MessageHelper.Warning("请选择串口。", "输入验证");
                return false;
            }

            if (SelectedBaudRate <= 0)
            {
                MessageHelper.Warning("请输入有效的波特率。", "输入验证");
                return false;
            }

            if (SelectedDataBits < 5 || SelectedDataBits > 8)
            {
                MessageHelper.Warning("数据位必须在5-8之间。", "输入验证");
                return false;
            }

            return true;
        }

        private void SaveConfiguration()
        {
            try
            {
                var config = new SerialPortConfig
                {
                    PortName = SelectedPortName,
                    BaudRate = SelectedBaudRate,
                    DataBits = SelectedDataBits,
                    Parity = SelectedParity,
                    StopBits = SelectedStopBits,
                    SlaveId = 1 // 从站地址不再保存到配置文件中
                };

                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    Converters = { new JsonStringEnumConverter() }
                };

                var jsonString = JsonSerializer.Serialize(config, options);
                File.WriteAllText(_configFilePath, jsonString);
            }
            catch (Exception ex)
            {
                MessageHelper.Error("保存配置文件失败", ex);
            }
        }
    }
}