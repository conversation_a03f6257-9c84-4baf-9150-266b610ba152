using GaugeCtrl.Core.Interfaces;
using GaugeCtrl.Communication.Models;

namespace GaugeCtrl.Communication.Interfaces
{
    /// <summary>
    /// 协议处理器基础接口
    /// 定义所有协议处理器的通用行为
    /// </summary>
    public interface IProtocolHandler : IDisposable
    {
        /// <summary>
        /// 协议处理器名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 是否已连接
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        event Action<bool>? ConnectionStatusChanged;

        /// <summary>
        /// 连接设备
        /// </summary>
        /// <param name="config">连接配置</param>
        /// <returns>连接是否成功</returns>
        Task<bool> ConnectAsync(IConnectionConfig config);

        /// <summary>
        /// 断开连接
        /// </summary>
        Task DisconnectAsync();

        /// <summary>
        /// 获取可用的串口列表
        /// </summary>
        /// <returns>串口名称列表</returns>
        string[] GetAvailablePorts();
    }
}