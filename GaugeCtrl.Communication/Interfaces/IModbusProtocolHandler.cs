using GaugeCtrl.Communication.Models;

namespace GaugeCtrl.Communication.Interfaces
{
    /// <summary>
    /// Modbus协议处理器接口
    /// </summary>
    public interface IModbusProtocolHandler : IProtocolHandler
    {
        /// <summary>
        /// Modbus驱动器数据更新事件
        /// </summary>
        event Action<ModbusDriverData>? ModbusDriverDataUpdated;

        /// <summary>
        /// 从站地址变更事件
        /// </summary>
        event Action<byte>? SlaveIdChanged;

        /// <summary>
        /// 当前从站地址
        /// </summary>
        byte CurrentSlaveId { get; set; }

        /// <summary>
        /// 读取单个保持寄存器
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="address">寄存器地址</param>
        /// <returns>寄存器值</returns>
        Task<ushort?> ReadHoldingRegisterAsync(byte slaveId, ushort address);

        /// <summary>
        /// 读取多个保持寄存器
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="quantity">数量</param>
        /// <returns>寄存器值数组</returns>
        Task<ushort[]?> ReadHoldingRegistersAsync(byte slaveId, ushort startAddress, ushort quantity);

        /// <summary>
        /// 写入单个寄存器
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="address">寄存器地址</param>
        /// <param name="value">值</param>
        /// <returns>写入是否成功</returns>
        Task<bool> WriteSingleRegisterAsync(byte slaveId, ushort address, ushort value);

        /// <summary>
        /// 写入多个寄存器
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="startAddress">起始地址</param>
        /// <param name="values">值数组</param>
        /// <returns>写入是否成功</returns>
        Task<bool> WriteMultipleRegistersAsync(byte slaveId, ushort startAddress, ushort[] values);

        /// <summary>
        /// 发送控制命令
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="command">控制命令</param>
        /// <returns>发送是否成功</returns>
        Task<bool> SendControlCommandAsync(byte slaveId, ControlCommand command);

        /// <summary>
        /// 设置控制速度
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="speed">速度值</param>
        /// <returns>设置是否成功</returns>
        Task<bool> SetControlSpeedAsync(byte slaveId, ushort speed);

        /// <summary>
        /// 设置控制模式
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="mode">控制模式</param>
        /// <returns>设置是否成功</returns>
        Task<bool> SetControlModeAsync(byte slaveId, ControlMode mode);

        /// <summary>
        /// 保存所有参数
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <returns>保存是否成功</returns>
        Task<bool> SaveAllParametersAsync(byte slaveId);

        /// <summary>
        /// 读取所有驱动器数据
        /// </summary>
        /// <param name="slaveId">从站地址</param>
        /// <param name="timeout">超时时间</param>
        /// <returns>读取是否成功</returns>
        Task<bool> ReadAllDriverDataAsync(byte slaveId, int timeout = 1000);
    }
}