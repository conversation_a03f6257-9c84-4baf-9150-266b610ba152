using GaugeCtrl.Core.Models;

namespace GaugeCtrl.Communication.Interfaces
{
    /// <summary>
    /// 示波器协议处理器接口
    /// </summary>
    public interface IOscilloscopeProtocolHandler : IProtocolHandler
    {
        /// <summary>
        /// 示波器数据接收事件
        /// </summary>
        event Action<OscilloscopeData>? OscilloscopeDataReceived;

        /// <summary>
        /// 发送数据
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <returns>发送是否成功</returns>
        Task<bool> SendDataAsync(byte[] data);
    }
}