using NLog;
using GaugeCtrl.Core.Models;
using GaugeCtrl.Core.Interfaces;
using GaugeCtrl.Communication.Interfaces;
using GaugeCtrl.Communication.Models;
using GaugeCtrl.Communication.ProtocolHandlers;

namespace GaugeCtrl.Communication
{
    /// <summary>
    /// 串口管理器
    /// 管理两个固定的串口实例：示波器协议和Modbus协议
    /// 根据view名称自动切换协议，避免重复初始化
    /// </summary>
    public class SerialPortManager : ISerialPortManager
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        // 两个固定的协议处理器实例，避免重复初始化
        private readonly OscilloscopeProtocolHandler _oscilloscopeHandler;
        private readonly ModbusProtocolHandler _modbusHandler;

        private IProtocolHandler? _currentHandler;
        private ProtocolType _currentProtocol = ProtocolType.None;
        private bool _disposed = false;
        private readonly object _lockObject = new object();

        /// <summary>
        /// 示波器数据接收事件
        /// </summary>
        public event Action<OscilloscopeData>? OscilloscopeDataReceived;

        /// <summary>
        /// Modbus驱动器数据更新事件
        /// </summary>
        public event Action<ModbusDriverData>? ModbusDriverDataUpdated;

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        public event Action<bool>? ConnectionStatusChanged;

        /// <summary>
        /// 构造函数 - 初始化两个固定的协议处理器实例
        /// </summary>
        public SerialPortManager()
        {
            // 初始化两个固定的协议处理器，避免重复创建
            _oscilloscopeHandler = new OscilloscopeProtocolHandler();
            _modbusHandler = new ModbusProtocolHandler();

            // 订阅事件
            _oscilloscopeHandler.OscilloscopeDataReceived += OnOscilloscopeDataReceived;
            _oscilloscopeHandler.ConnectionStatusChanged += OnConnectionStatusChanged;

            _modbusHandler.ModbusDriverDataUpdated += OnModbusDriverDataUpdated;
            _modbusHandler.ConnectionStatusChanged += OnConnectionStatusChanged;

            Logger.Info("串口管理器初始化完成，已创建示波器和Modbus协议处理器实例");
        }

        /// <summary>
        /// 获取当前协议类型
        /// </summary>
        public ProtocolType CurrentProtocol => _currentProtocol;

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected => _currentHandler?.IsConnected == true;

        /// <summary>
        /// 获取可用的串口列表
        /// </summary>
        /// <returns>串口名称列表</returns>
        public string[] GetAvailablePorts()
        {
            return _currentHandler?.GetAvailablePorts() ?? _modbusHandler.GetAvailablePorts();
        }

        /// <summary>
        /// 根据view名称智能连接串口
        /// </summary>
        /// <param name="viewName">视图名称</param>
        /// <param name="serialConfig">串口配置（示波器协议用）</param>
        /// <param name="modbusConfig">Modbus配置（Modbus协议用）</param>
        /// <returns>连接是否成功</returns>
        public async Task<bool> ConnectByViewAsync(string viewName, SerialPortConfig? serialConfig = null, ModbusConfig? modbusConfig = null)
        {
            var protocolType = GetProtocolTypeByView(viewName);

            switch (protocolType)
            {
                case ProtocolType.Oscilloscope:
                    if (serialConfig == null)
                    {
                        Logger.Error($"示波器协议连接需要提供串口配置，视图: {viewName}");
                        return false;
                    }
                    return await SwitchToProtocolAndConnectAsync(ProtocolType.Oscilloscope, serialConfig);

                case ProtocolType.Modbus:
                    if (modbusConfig == null)
                    {
                        Logger.Error($"Modbus协议连接需要提供Modbus配置，视图: {viewName}");
                        return false;
                    }
                    return await SwitchToProtocolAndConnectAsync(ProtocolType.Modbus, modbusConfig);

                default:
                    Logger.Error($"不支持的协议类型: {protocolType}，视图: {viewName}");
                    return false;
            }
        }

        /// <summary>
        /// 连接串口（示波器协议）
        /// </summary>
        /// <param name="config">串口配置</param>
        /// <returns>连接是否成功</returns>
        public async Task<bool> ConnectOscilloscopeAsync(SerialPortConfig config)
        {
            return await SwitchToProtocolAndConnectAsync(ProtocolType.Oscilloscope, config);
        }

        /// <summary>
        /// 连接Modbus设备
        /// </summary>
        /// <param name="config">Modbus配置</param>
        /// <returns>连接是否成功</returns>
        public async Task<bool> ConnectModbusAsync(ModbusConfig config)
        {
            return await SwitchToProtocolAndConnectAsync(ProtocolType.Modbus, config);
        }

        /// <summary>
        /// 根据视图名称获取协议类型
        /// </summary>
        /// <param name="viewName">视图名称</param>
        /// <returns>协议类型</returns>
        private ProtocolType GetProtocolTypeByView(string viewName)
        {
            return viewName switch
            {
                "OscilloscopeView" => ProtocolType.Oscilloscope,
                "ParameterSettings" => ProtocolType.Modbus,
                "MotionSimulation" => ProtocolType.Modbus,
                "FirmwareUpgrade" => ProtocolType.Modbus,
                "SerialSettings" => ProtocolType.Modbus,
                "General" => ProtocolType.Modbus,
                _ => ProtocolType.Modbus // 默认使用Modbus协议
            };
        }

        /// <summary>
        /// 断开当前连接（内部方法）
        /// </summary>
        private async Task DisconnectCurrentAsync()
        {
            if (_currentHandler != null)
            {
                try
                {
                    await _currentHandler.DisconnectAsync();
                    Logger.Debug($"已断开{_currentProtocol}协议连接");
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, $"断开{_currentProtocol}协议连接时发生错误");
                }

                _currentHandler = null;
                _currentProtocol = ProtocolType.None;
            }
        }

        /// <summary>
        /// 断开连接（公共方法）
        /// </summary>
        public async Task DisconnectAsync()
        {
            try
            {
                await DisconnectCurrentAsync();
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "断开连接时发生错误");
            }
            finally
            {
                ConnectionStatusChanged?.Invoke(false);
            }
        }

        /// <summary>
        /// 发送数据（仅示波器协议）
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <returns>发送是否成功</returns>
        public async Task<bool> SendDataAsync(byte[] data)
        {
            if (_currentHandler is IOscilloscopeProtocolHandler oscilloscopeHandler)
            {
                return await oscilloscopeHandler.SendDataAsync(data);
            }
            
            Logger.Warn("当前协议不支持发送数据操作");
            return false;
        }



        /// <summary>
        /// 获取当前的协议处理器
        /// </summary>
        /// <returns>当前协议处理器</returns>
        public IProtocolHandler? GetCurrentHandler()
        {
            return _currentHandler;
        }

        /// <summary>
        /// 获取Modbus协议处理器
        /// </summary>
        /// <returns>Modbus协议处理器，如果当前不是Modbus协议则返回null</returns>
        public IModbusProtocolHandler? GetModbusHandler()
        {
            return _currentHandler as IModbusProtocolHandler;
        }

        /// <summary>
        /// 获取示波器协议处理器
        /// </summary>
        /// <returns>示波器协议处理器，如果当前不是示波器协议则返回null</returns>
        public IOscilloscopeProtocolHandler? GetOscilloscopeHandler()
        {
            return _currentHandler as IOscilloscopeProtocolHandler;
        }



        /// <summary>
        /// 切换到指定协议并连接
        /// 先断开当前连接，再切换到新协议
        /// </summary>
        /// <param name="protocolType">协议类型</param>
        /// <param name="config">连接配置</param>
        /// <returns>连接是否成功</returns>
        private async Task<bool> SwitchToProtocolAndConnectAsync(ProtocolType protocolType, IConnectionConfig config)
        {
            lock (_lockObject)
            {
                if (_currentProtocol != ProtocolType.None && _currentProtocol != protocolType)
                {
                    Logger.Info($"协议切换: {_currentProtocol} → {protocolType}");
                }
            }

            try
            {
                // 先断开当前连接
                await DisconnectCurrentAsync();

                // 选择对应的协议处理器（使用预初始化的实例）
                _currentHandler = protocolType switch
                {
                    ProtocolType.Oscilloscope => _oscilloscopeHandler,
                    ProtocolType.Modbus => _modbusHandler,
                    _ => throw new ArgumentException($"不支持的协议类型: {protocolType}")
                };

                // 连接新协议
                var success = await _currentHandler.ConnectAsync(config);
                if (success)
                {
                    _currentProtocol = protocolType;
                    Logger.Info($"{protocolType}协议连接成功");
                }
                else
                {
                    Logger.Error($"{protocolType}协议连接失败");
                    _currentHandler = null;
                    _currentProtocol = ProtocolType.None;
                }
                return success;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"切换到{protocolType}协议时发生错误");
                _currentHandler = null;
                _currentProtocol = ProtocolType.None;
                return false;
            }
        }



        /// <summary>
        /// 连接状态变化处理
        /// </summary>
        private void OnConnectionStatusChanged(bool isConnected)
        {
            if (!isConnected)
            {
                _currentProtocol = ProtocolType.None;
            }
            ConnectionStatusChanged?.Invoke(isConnected);
        }

        /// <summary>
        /// 示波器数据接收处理
        /// </summary>
        private void OnOscilloscopeDataReceived(OscilloscopeData data)
        {
            OscilloscopeDataReceived?.Invoke(data);
        }

        /// <summary>
        /// Modbus驱动器数据更新处理
        /// </summary>
        private void OnModbusDriverDataUpdated(ModbusDriverData data)
        {
            ModbusDriverDataUpdated?.Invoke(data);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    DisconnectAsync().Wait(1000);

                    // 释放两个协议处理器实例
                    _oscilloscopeHandler?.Dispose();
                    _modbusHandler?.Dispose();

                    Logger.Info("串口管理器资源释放完成");
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "释放资源时发生错误");
                }
                finally
                {
                    _disposed = true;
                }
            }
        }
    }
}