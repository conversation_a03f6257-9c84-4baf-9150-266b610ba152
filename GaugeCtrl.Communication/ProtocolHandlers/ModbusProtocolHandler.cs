using System.IO.Ports;
using NLog;
using GaugeCtrl.Core.Interfaces;
using GaugeCtrl.Communication.Interfaces;
using GaugeCtrl.Communication.Models;
using TouchSocket.Core;
using TouchSocket.Modbus;
using TouchSocket.SerialPorts;

namespace GaugeCtrl.Communication.ProtocolHandlers
{
    /// <summary>
    /// Modbus协议处理器实现
    /// 负责Modbus协议的具体通讯逻辑
    /// </summary>
    public class ModbusProtocolHandler : IModbusProtocolHandler
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private ModbusRtuMaster? _modbusMaster;
        private ModbusConfig? _config;
        private bool _disposed = false;
        private byte _currentSlaveId = 1; // 当前从站地址

        public string Name => "Modbus协议处理器";
        public bool IsConnected => _modbusMaster?.Online == true;

        public event Action<bool>? ConnectionStatusChanged;
        public event Action<ModbusDriverData>? ModbusDriverDataUpdated;
        public event Action<byte>? SlaveIdChanged; // 从站地址变更事件

        /// <summary>
        /// 当前从站地址
        /// </summary>
        public byte CurrentSlaveId
        {
            get => _currentSlaveId;
            set
            {
                if (_currentSlaveId != value)
                {
                    _currentSlaveId = value;
                    Logger.Info($"从站地址已更改为: {value}");
                    SlaveIdChanged?.Invoke(value);
                }
            }
        }

        public string[] GetAvailablePorts()
        {
            try
            {
                return SerialPort.GetPortNames();
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "获取可用串口列表时发生错误");
                return Array.Empty<string>();
            }
        }

        public async Task<bool> ConnectAsync(IConnectionConfig config)
        {
            if (config is not ModbusConfig modbusConfig)
            {
                Logger.Error("Modbus协议处理器需要ModbusConfig配置");
                return false;
            }

            try
            {
                if (_modbusMaster != null)
                {
                    await DisconnectAsync();
                }

                _config = modbusConfig;
                _modbusMaster = new ModbusRtuMaster();

                var touchConfig = new TouchSocketConfig()
                    .SetSerialPortOption(new SerialPortOption()
                    {
                        PortName = modbusConfig.PortName,
                        BaudRate = modbusConfig.BaudRate,
                        DataBits = modbusConfig.DataBits,
                        Parity = modbusConfig.Parity,
                        StopBits = modbusConfig.StopBits
                    });

                await _modbusMaster.SetupAsync(touchConfig);
                await _modbusMaster.ConnectAsync(5000, CancellationToken.None);

                modbusConfig.IsConnected = true;
                ConnectionStatusChanged?.Invoke(true);

                Logger.Info($"Modbus连接成功: {modbusConfig.PortName}, 波特率: {modbusConfig.BaudRate}, 从站地址: {modbusConfig.SlaveId}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"连接Modbus设备失败: {modbusConfig.PortName}");
                modbusConfig.IsConnected = false;
                ConnectionStatusChanged?.Invoke(false);
                return false;
            }
        }

        public async Task DisconnectAsync()
        {
            try
            {
                if (_modbusMaster != null)
                {
                    await _modbusMaster.CloseAsync("手动断开连接");
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "断开Modbus连接时发生错误");
            }
            finally
            {
                _modbusMaster?.Dispose();
                _modbusMaster = null;
                ConnectionStatusChanged?.Invoke(false);
            }
        }

        public async Task<ushort?> ReadHoldingRegisterAsync(byte slaveId, ushort address)
        {
            try
            {
                if (_modbusMaster == null)
                {
                    Logger.Warn("Modbus未连接，无法读取寄存器");
                    return null;
                }

                // 获取配置的字节序，默认为大端序（Modbus标准）
                var endianType = _config?.EndianType ?? EndianType.Big;

                var response = await _modbusMaster.ReadHoldingRegistersAsync(slaveId, address, 1);
                var reader = response.CreateReader();

                // 使用 TouchSocket 的字节序转换
                var value = reader.ReadUInt16(endianType);

                Logger.Debug($"读取寄存器成功 - 地址: 0x{address:X4}, 值: 0x{value:X4}, 字节序: {endianType}");
                return value;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"读取寄存器失败 - 地址: 0x{address:X4}");
                return null;
            }
        }

        public async Task<ushort[]?> ReadHoldingRegistersAsync(byte slaveId, ushort startAddress, ushort quantity)
        {
            try
            {
                if (_modbusMaster == null)
                {
                    Logger.Warn("Modbus未连接");
                    return null;
                }

                // 获取配置的字节序，默认为大端序（Modbus标准）
                var endianType = _config?.EndianType ?? EndianType.Big;

                var response = await _modbusMaster.ReadHoldingRegistersAsync(slaveId, startAddress, quantity);
                var reader = response.CreateReader();

                // 使用 TouchSocket 的字节序转换
                var values = reader.ToUInt16s(endianType).ToArray();

                Logger.Debug($"读取寄存器成功 - 起始地址: 0x{startAddress:X4}, 数量: {quantity}, 字节序: {endianType}");
                return values;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"读取保持寄存器失败: SlaveId={slaveId}, StartAddress=0x{startAddress:X4}, Quantity={quantity}");
                return null;
            }
        }

        public async Task<bool> WriteSingleRegisterAsync(byte slaveId, ushort address, ushort value)
        {
            try
            {
                if (_modbusMaster == null)
                {
                    Logger.Warn("Modbus未连接");
                    return false;
                }

                // 获取配置的字节序，默认为大端序（Modbus标准）
                var endianType = _config?.EndianType ?? EndianType.Big;

                Logger.Debug($"写入单个寄存器 - 地址: 0x{address:X4}, 值: 0x{value:X4}, 字节序: {endianType}");

                await _modbusMaster.WriteSingleRegisterAsync(slaveId, address, (short)value);
                Logger.Debug($"写入寄存器成功 - 地址: 0x{address:X4}, 值: 0x{value:X4}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"写入寄存器失败 - 地址: 0x{address:X4}, 值: 0x{value:X4}");
                return false;
            }
        }

        public async Task<bool> WriteMultipleRegistersAsync(byte slaveId, ushort startAddress, ushort[] values)
        {
            try
            {
                if (_modbusMaster == null)
                {
                    Logger.Warn("Modbus未连接");
                    return false;
                }

                // 获取配置的字节序，默认为大端序（Modbus标准）
                var endianType = _config?.EndianType ?? EndianType.Big;

                // 使用 TouchSocket 的 ValueByteBlock 进行字节序转换
                using var valueByteBlock = new ValueByteBlock(values.Length * 2);

                Logger.Debug($"使用字节序: {GetEndianTypeDescription(endianType)}");

                foreach (var value in values)
                {
                    valueByteBlock.WriteUInt16(value, endianType);
                    Logger.Debug($"寄存器值转换: {value} (0x{value:X4}) -> 字节序: {endianType}");
                }

                await _modbusMaster.WriteMultipleRegistersAsync(slaveId, startAddress, valueByteBlock.ToArray());
                Logger.Debug($"写入寄存器成功 - 起始地址: 0x{startAddress:X4}, 数量: {values.Length}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"写入寄存器失败 - 起始地址: 0x{startAddress:X4}, 数量: {values.Length}");
                return false;
            }
        }

        public async Task<bool> SendControlCommandAsync(byte slaveId, ControlCommand command)
        {
            return await WriteSingleRegisterAsync(slaveId, ModbusRegisters.COMM_CONTROL_COMMAND, (ushort)command);
        }

        public async Task<bool> SetControlSpeedAsync(byte slaveId, ushort speed)
        {
            if (speed > 3000)
            {
                Logger.Warn($"速度值超出范围: {speed}, 最大值为3000");
                return false;
            }
            return await WriteSingleRegisterAsync(slaveId, ModbusRegisters.COMM_CONTROL_SPEED, speed);
        }

        public async Task<bool> SetControlModeAsync(byte slaveId, ControlMode mode)
        {
            return await WriteSingleRegisterAsync(slaveId, ModbusRegisters.COMM_CONTROL_MODE, (ushort)mode);
        }

        public async Task<bool> SaveAllParametersAsync(byte slaveId)
        {
            try
            {
                if (_modbusMaster == null)
                {
                    Logger.Warn("Modbus未连接");
                    return false;
                }

                var request = new ModbusRequest((FunctionCode)0x30);
                request.SlaveId = slaveId;
                request.StartingAddress = 0x0034;
                request.Quantity = 1;

                var response = await _modbusMaster.SendModbusRequestAsync(request, _config?.Timeout ?? 1000, CancellationToken.None);
                Logger.Info("断电保存参数成功");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "断电保存参数失败");
                return false;
            }
        }

        public async Task<bool> ReadAllDriverDataAsync(byte slaveId, int timeout = 1000)
        {
            try
            {
                if (_modbusMaster == null)
                {
                    Logger.Warn("Modbus未连接");
                    return false;
                }

                var driverData = new ModbusDriverData();

                // 读取控制参数
                var controlRegisters = await ReadHoldingRegistersAsync(slaveId, ModbusRegisters.COMM_CONTROL_COMMAND, 10);
                if (controlRegisters != null && controlRegisters.Length >= 10)
                {
                    driverData.ControlCommand = (ControlCommand)controlRegisters[0];
                    driverData.ControlSpeed = controlRegisters[1];
                    driverData.MotorPolePairs = controlRegisters[2];
                    driverData.AccelTime = controlRegisters[3];
                    driverData.DecelTime = controlRegisters[4];
                    driverData.ControlMode = (ControlMode)controlRegisters[6];
                    driverData.SlaveAddress = (byte)controlRegisters[8];
                    driverData.BaudRate = controlRegisters[9];
                }

                // 读取状态寄存器
                var statusRegisters = await ReadHoldingRegistersAsync(slaveId, ModbusRegisters.DRIVER_STATUS, 4);
                if (statusRegisters != null && statusRegisters.Length >= 4)
                {
                    driverData.DriverStatus = (DriverStatus)statusRegisters[0];
                    driverData.FaultCode = (FaultCode)statusRegisters[2];
                    driverData.DriverModel = statusRegisters[3];
                }

                // 读取监控寄存器
                var monitorRegisters = await ReadHoldingRegistersAsync(slaveId, ModbusRegisters.SET_FREQUENCY, 18);
                if (monitorRegisters != null && monitorRegisters.Length >= 18)
                {
                    driverData.SetFrequency = monitorRegisters[0];
                    driverData.OutputFrequency = monitorRegisters[1];
                    driverData.RampFrequency = monitorRegisters[2];
                    driverData.OutputVoltage = monitorRegisters[3];
                    driverData.OutputCurrent = monitorRegisters[4];
                    driverData.SetSpeed = monitorRegisters[5];
                    driverData.MotorSpeed = monitorRegisters[6];
                    driverData.MotorPower = monitorRegisters[7];
                    driverData.DcBusVoltage = monitorRegisters[8];
                    driverData.HallValue = monitorRegisters[9];
                    driverData.FirmwareVersion = monitorRegisters[10];
                    driverData.LastFaultType = (FaultCode)monitorRegisters[11];
                    driverData.InputTerminalStatus = monitorRegisters[13];
                    driverData.OutputTerminalStatus = monitorRegisters[14];
                    driverData.Analog1Input = monitorRegisters[15];
                    driverData.Analog2Input = monitorRegisters[16];
                }

                ModbusDriverDataUpdated?.Invoke(driverData);
                Logger.Debug("读取所有驱动器数据成功");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "读取驱动器数据失败");
                return false;
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    DisconnectAsync().Wait(1000);
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "释放Modbus协议处理器资源时发生错误");
                }
                finally
                {
                    _disposed = true;
                }
            }
        }

        /// <summary>
        /// 获取字节序的描述信息
        /// </summary>
        /// <param name="endianType">字节序类型</param>
        /// <returns>描述信息</returns>
        private static string GetEndianTypeDescription(EndianType endianType)
        {
            return endianType switch
            {
                EndianType.Big => "大端序（Big Endian）- 高字节在前，Modbus标准",
                EndianType.Little => "小端序（Little Endian）- 低字节在前，Intel x86标准",
                EndianType.BigSwap => "大端交换序（Big Swap）- BADC字节序",
                EndianType.LittleSwap => "小端交换序（Little Swap）- CDAB字节序",
                _ => $"未知字节序: {endianType}"
            };
        }
    }
}