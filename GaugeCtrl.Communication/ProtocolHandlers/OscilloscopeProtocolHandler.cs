using System.IO.Ports;
using NLog;
using GaugeCtrl.Core.Models;
using GaugeCtrl.Communication.Interfaces;
using GaugeCtrl.Core.Interfaces;
using TouchSocket.Core;
using TouchSocket.SerialPorts;
using TouchSocket.Sockets;

namespace GaugeCtrl.Communication.ProtocolHandlers
{
    /// <summary>
    /// 示波器协议处理器实现
    /// 负责示波器协议的具体通讯逻辑
    /// </summary>
    public class OscilloscopeProtocolHandler : IOscilloscopeProtocolHandler
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private SerialPortClient? _client;
        private bool _disposed = false;

        public string Name => "示波器协议处理器";
        public bool IsConnected => _client?.Online == true;

        public event Action<bool>? ConnectionStatusChanged;
        public event Action<OscilloscopeData>? OscilloscopeDataReceived;

        public string[] GetAvailablePorts()
        {
            try
            {
                return SerialPort.GetPortNames();
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "获取可用串口列表时发生错误");
                return Array.Empty<string>();
            }
        }

        public async Task<bool> ConnectAsync(IConnectionConfig config)
        {
            if (config is not SerialPortConfig serialConfig)
            {
                Logger.Error("示波器协议处理器需要SerialPortConfig配置");
                return false;
            }

            try
            {
                await DisconnectAsync();

                _client = new SerialPortClient();
                _client.Received = OnDataReceived;
                _client.Closed = OnDisconnected;

                var touchConfig = new TouchSocketConfig()
                    .SetSerialPortOption(new SerialPortOption()
                    {
                        PortName = serialConfig.PortName,
                        BaudRate = serialConfig.BaudRate,
                        DataBits = serialConfig.DataBits,
                        Parity = serialConfig.Parity,
                        StopBits = serialConfig.StopBits
                    })
                    .SetSerialDataHandlingAdapter(() => new OscilloscopeDataAdapter());

                await _client.SetupAsync(touchConfig);
                await _client.ConnectAsync();

                if (_client.Online)
                {
                    serialConfig.IsConnected = true;
                    ConnectionStatusChanged?.Invoke(true);
                    Logger.Info($"示波器协议连接成功: {serialConfig.PortName}, 波特率: {serialConfig.BaudRate}");
                    return true;
                }
                else
                {
                    Logger.Error($"示波器协议连接失败: {serialConfig.PortName}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"连接示波器协议失败: {serialConfig.PortName}");
                serialConfig.IsConnected = false;
                ConnectionStatusChanged?.Invoke(false);
                return false;
            }
        }

        public async Task DisconnectAsync()
        {
            try
            {
                if (_client?.Online == true)
                {
                    await _client.CloseAsync();
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "断开示波器协议连接时发生错误");
            }
            finally
            {
                _client?.Dispose();
                _client = null;
                ConnectionStatusChanged?.Invoke(false);
            }
        }

        public async Task<bool> SendDataAsync(byte[] data)
        {
            try
            {
                if (_client?.Online != true)
                {
                    Logger.Warn("示波器协议未连接，无法发送数据");
                    return false;
                }

                await _client.SendAsync(data);
                Logger.Debug($"发送数据成功，长度: {data.Length} 字节");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "发送数据时发生错误");
                return false;
            }
        }

        private async Task OnDataReceived(ISerialPortClient client, ReceivedDataEventArgs e)
        {
            try
            {
                if (e.RequestInfo is OscilloscopeDataRequestInfo oscilloscopeRequest)
                {
                    var oscilloscopeData = oscilloscopeRequest.ToOscilloscopeData();
                    Logger.Debug($"解析示波器数据成功 - 通道1: {oscilloscopeData.Channel1Value}, 通道2: {oscilloscopeData.Channel2Value}");
                    OscilloscopeDataReceived?.Invoke(oscilloscopeData);
                }
                else
                {
                    var data = e.ByteBlock.ToArray();
                    Logger.Debug($"接收到未解析数据，长度: {data.Length} 字节");
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "处理示波器数据时发生错误");
            }

            await Task.CompletedTask;
        }

        private async Task OnDisconnected(ISerialPortClient client, ClosedEventArgs e)
        {
            Logger.Info($"示波器协议连接断开: {e.Message}");
            ConnectionStatusChanged?.Invoke(false);
            await Task.CompletedTask;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    DisconnectAsync().Wait(1000);
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "释放示波器协议处理器资源时发生错误");
                }
                finally
                {
                    _disposed = true;
                }
            }
        }
    }
}