using TouchSocket.Core;
using GaugeCtrl.Core.Models;

namespace GaugeCtrl.Communication
{
    /// <summary>
    /// 示波器数据请求信息
    /// 协议格式：0x55 + 两个UINT16 + 0xAA
    /// </summary>
    public class OscilloscopeDataRequestInfo : IFixedHeaderRequestInfo
    {
        private const byte FRAME_HEADER = 0x55;
        private const byte FRAME_TAIL = 0xAA;
        private const int DATA_LENGTH = 5; // 2(ch1) + 2(ch2) + 1(tail)

        /// <summary>
        /// 接口实现，标识数据长度
        /// </summary>
        public int BodyLength { get; private set; }

        /// <summary>
        /// 通道1数值
        /// </summary>
        public ushort Channel1Value { get; set; }

        /// <summary>
        /// 通道2数值
        /// </summary>
        public ushort Channel2Value { get; set; }

        /// <summary>
        /// 原始数据
        /// </summary>
        public byte[] Body { get; set; } = Array.Empty<byte>();

        /// <summary>
        /// 解析包头
        /// </summary>
        /// <param name="header">包头数据</param>
        /// <returns>解析是否成功</returns>
        public bool OnParsingHeader(ReadOnlySpan<byte> header)
        {
            // 验证帧头
            if (header.Length != 1 || header[0] != FRAME_HEADER)
            {
                return false;
            }

            // 设置后续数据长度（2个UINT16 + 帧尾）
            this.BodyLength = DATA_LENGTH;
            return true;
        }

        /// <summary>
        /// 解析包体
        /// </summary>
        /// <param name="body">包体数据</param>
        /// <returns>解析是否成功</returns>
        public bool OnParsingBody(ReadOnlySpan<byte> body)
        {
            if (body.Length != this.BodyLength)
            {
                return false;
            }

            // 验证帧尾
            if (body[4] != FRAME_TAIL)
            {
                return false;
            }

            try
            {
                // 解析两个UINT16值（小端序）
                this.Channel1Value = BitConverter.ToUInt16(body.Slice(0, 2));
                this.Channel2Value = BitConverter.ToUInt16(body.Slice(2, 2));
                this.Body = body.ToArray();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 转换为示波器数据模型
        /// </summary>
        /// <returns>示波器数据</returns>
        public OscilloscopeData ToOscilloscopeData()
        {
            return new OscilloscopeData
            {
                Channel1Value = this.Channel1Value,
                Channel2Value = this.Channel2Value
            };
        }
    }
}