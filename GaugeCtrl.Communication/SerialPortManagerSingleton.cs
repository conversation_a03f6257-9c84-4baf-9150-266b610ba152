using NLog;
using GaugeCtrl.Communication.Interfaces;

namespace GaugeCtrl.Communication
{
    /// <summary>
    /// 串口管理器单例
    /// 提供全局统一的串口管理器实例，同时保持接口的灵活性
    /// </summary>
    public static class SerialPortManagerSingleton
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private static readonly object _lockObject = new();
        private static ISerialPortManager? _instance;

        /// <summary>
        /// 获取串口管理器实例
        /// </summary>
        public static ISerialPortManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lockObject)
                    {
                        if (_instance == null)
                        {
                            _instance = new SerialPortManager();
                            Logger.Info("创建串口管理器单例实例");
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 设置自定义实例（主要用于测试）
        /// </summary>
        /// <param name="instance">自定义实例</param>
        public static void SetInstance(ISerialPortManager instance)
        {
            lock (_lockObject)
            {
                _instance?.Dispose();
                _instance = instance;
                Logger.Info("设置自定义串口管理器实例");
            }
        }

        /// <summary>
        /// 重置实例（主要用于测试清理）
        /// </summary>
        public static void Reset()
        {
            lock (_lockObject)
            {
                _instance?.Dispose();
                _instance = null;
                Logger.Info("重置串口管理器实例");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public static void Dispose()
        {
            lock (_lockObject)
            {
                _instance?.Dispose();
                _instance = null;
                Logger.Info("释放串口管理器单例资源");
            }
        }
    }
}
