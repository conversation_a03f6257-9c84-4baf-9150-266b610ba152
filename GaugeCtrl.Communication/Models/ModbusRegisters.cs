namespace GaugeCtrl.Communication.Models
{
    /// <summary>
    /// Modbus寄存器地址常量
    /// </summary>
    public static class ModbusRegisters
    {
        // 控制寄存器地址
        public const ushort COMM_CONTROL_COMMAND = 0x2000;     // 通讯控制命令
        public const ushort COMM_CONTROL_SPEED = 0x2001;       // 通讯控制速度
        public const ushort MOTOR_POLE_PAIRS = 0x2002;         // 电机极对数
        public const ushort ACCEL_TIME = 0x2003;               // 加速时间
        public const ushort DECEL_TIME = 0x2004;               // 减速时间
        public const ushort CONTROL_MODE = 0x2006;             // 控制模式选择
        public const ushort COMM_CONTROL_MODE = 0x2006;        // 通讯控制模式（别名）
        public const ushort SLAVE_ADDRESS = 0x2008;            // 从站地址
        public const ushort BAUD_RATE = 0x2009;                // 波特率
        public const ushort FACTORY_RESET = 0x200F;            // 恢复出厂设置
        public const ushort STOP_MODE = 0x2012;                // 停车方式
        
        // 状态寄存器地址
        public const ushort DRIVER_STATUS = 0x2100;            // 驱动器状态
        public const ushort FAULT_CODE = 0x2102;               // 故障代码
        public const ushort DRIVER_MODEL = 0x2103;             // 驱动器型号
        
        // 监控寄存器地址
        public const ushort SET_FREQUENCY = 0x3000;            // 设定频率
        public const ushort OUTPUT_FREQUENCY = 0x3001;         // 输出频率
        public const ushort RAMP_FREQUENCY = 0x3002;           // 斜坡给定频率
        public const ushort OUTPUT_VOLTAGE = 0x3003;           // 输出电压
        public const ushort OUTPUT_CURRENT = 0x3004;           // 输出电流
        public const ushort SET_SPEED = 0x3005;                // 设定转速
        public const ushort MOTOR_SPEED = 0x3006;              // 电机输出转速
        public const ushort MOTOR_POWER = 0x3007;              // 电机输出功率
        public const ushort DC_BUS_VOLTAGE = 0x3008;           // 直流母线电压
        public const ushort HALL_VALUE = 0x3009;               // 霍尔值
        public const ushort FIRMWARE_VERSION = 0x300A;         // 固件版本
        public const ushort LAST_FAULT_TYPE = 0x300B;          // 最近一次故障类型
        public const ushort INPUT_TERMINAL_STATUS = 0x300D;    // 输入端子状态
        public const ushort OUTPUT_TERMINAL_STATUS = 0x300E;   // 输出端子状态
        public const ushort ANALOG1_INPUT = 0x300F;            // 模拟量1输入电压
        public const ushort ANALOG2_INPUT = 0x3010;            // 模拟量2输入电压
    }
}
