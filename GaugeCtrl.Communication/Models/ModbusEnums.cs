namespace GaugeCtrl.Communication.Models
{
    /// <summary>
    /// 控制命令枚举
    /// </summary>
    public enum ControlCommand : ushort
    {
        None = 0x0000,          // 无指令
        ForwardRun = 0x0001,    // 正转运行
        ReverseRun = 0x0002,    // 反转运行
        Stop = 0x0005,          // 停机
        FreeStop = 0x0006,      // 自由停机
        FaultReset = 0x0007,    // 故障复位
        BrakeStop = 0x0009      // 刹车停机
    }

    /// <summary>
    /// 控制模式枚举
    /// </summary>
    public enum ControlMode : ushort
    {
        IOAndInternalKnob = 0x0000,     // IO+内部旋钮控制
        FullCommunication = 0x0001,     // 完全通讯控制
        CommunicationSpeed = 0x0002,    // 通讯控制速度
        IOAndExternalAnalog = 0x0003    // IO+外部模拟量控制
    }

    /// <summary>
    /// 驱动器状态枚举
    /// </summary>
    public enum DriverStatus : ushort
    {
        ForwardRunning = 0x0001,    // 正转运行中
        ReverseRunning = 0x0002,    // 反转运行中
        Stopping = 0x0003,          // 驱动器停止中
        Fault = 0x0004,             // 驱动器故障中
        DriveOff = 0x0005,          // 驱动OFF状态
        ElectronicBrake = 0x0006    // 电子刹车状态
    }

    /// <summary>
    /// 故障代码枚举
    /// </summary>
    public enum FaultCode : ushort
    {
        None = 0x0000,                  // 无故障
        HallFault = 0x0001,             // 霍尔故障
        MotorPhaseOpen = 0x0002,        // 电机缺相
        OverVoltage = 0x0003,           // 驱动器电压过高
        UnderVoltage = 0x0004,          // 驱动器电压过低
        OverCurrent = 0x0005,           // 驱动器过流
        MotorOverload = 0x0006,         // 电机过载
        MotorOverspeed = 0x0007,        // 电机超速
        MotorStall = 0x0008,            // 电机堵转
        CurrentSampleAFault = 0x0009,   // 电流采样电路a异常
        CurrentSampleBFault = 0x000A,   // 电流采样电路b异常
        SpeedErrorTooLarge = 0x000B,    // 速度误差过大
        MotorStepLoss = 0x000C,         // 电机失步
        NoCommutation = 0x000D,         // 未换相
        CommutationFailed = 0x000E,     // 换相失败
        PositionFollowTimeout = 0x000F, // 位置跟随超时
        SpeedFollowTimeout = 0x0010,    // 速度跟随超时
        InternalStorageFault = 0x0012,  // 驱动器内部存储异常
        OverTemperature = 0x0013        // 驱动器温度过高
    }
}
