using CommunityToolkit.Mvvm.ComponentModel;
using GaugeCtrl.Core.Interfaces;
using TouchSocket.Core;

namespace GaugeCtrl.Communication.Models
{
    /// <summary>
    /// Modbus配置模型
    /// </summary>
    public partial class ModbusConfig : ObservableObject, IConnectionConfig
    {
        [ObservableProperty]
        private string _portName = "COM1";

        [ObservableProperty]
        private int _baudRate = 9600;

        [ObservableProperty]
        private System.IO.Ports.Parity _parity = System.IO.Ports.Parity.Even;

        [ObservableProperty]
        private System.IO.Ports.StopBits _stopBits = System.IO.Ports.StopBits.One;

        [ObservableProperty]
        private int _dataBits = 8;

        [ObservableProperty]
        private byte _slaveId = 1;

        [ObservableProperty]
        private int _timeout = 1000;

        [ObservableProperty]
        private bool _isConnected;

        [ObservableProperty]
        private EndianType _endianType = EndianType.Big;
    }
}
