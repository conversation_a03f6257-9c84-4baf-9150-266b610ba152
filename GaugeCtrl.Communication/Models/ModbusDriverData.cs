using CommunityToolkit.Mvvm.ComponentModel;

namespace GaugeCtrl.Communication.Models
{
    /// <summary>
    /// Modbus驱动器数据模型
    /// </summary>
    public partial class ModbusDriverData : ObservableObject
    {
        // 控制参数属性
        [ObservableProperty]
        private ControlCommand _controlCommand;

        [ObservableProperty]
        private ushort _controlSpeed;

        [ObservableProperty]
        private ushort _motorPolePairs;

        [ObservableProperty]
        private ushort _accelTime;

        [ObservableProperty]
        private ushort _decelTime;

        [ObservableProperty]
        private ControlMode _controlMode;

        [ObservableProperty]
        private byte _slaveAddress;

        [ObservableProperty]
        private ushort _baudRate;

        [ObservableProperty]
        private ushort _stopMode;

        // 状态监控属性
        [ObservableProperty]
        private DriverStatus _driverStatus;

        [ObservableProperty]
        private FaultCode _faultCode;

        [ObservableProperty]
        private ushort _driverModel;

        [ObservableProperty]
        private ushort _setFrequency;

        [ObservableProperty]
        private ushort _outputFrequency;

        [ObservableProperty]
        private ushort _rampFrequency;

        [ObservableProperty]
        private ushort _outputVoltage;

        [ObservableProperty]
        private ushort _outputCurrent;

        [ObservableProperty]
        private ushort _setSpeed;

        [ObservableProperty]
        private ushort _motorSpeed;

        [ObservableProperty]
        private ushort _motorPower;

        [ObservableProperty]
        private ushort _dcBusVoltage;

        [ObservableProperty]
        private ushort _hallValue;

        [ObservableProperty]
        private ushort _firmwareVersion;

        [ObservableProperty]
        private FaultCode _lastFaultType;

        [ObservableProperty]
        private ushort _inputTerminalStatus;

        [ObservableProperty]
        private ushort _outputTerminalStatus;

        [ObservableProperty]
        private ushort _analog1Input;

        [ObservableProperty]
        private ushort _analog2Input;
    }
}
