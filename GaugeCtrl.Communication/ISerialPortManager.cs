using GaugeCtrl.Communication.Models;
using GaugeCtrl.Communication.Interfaces;
using GaugeCtrl.Core.Models;

namespace GaugeCtrl.Communication
{
    /// <summary>
    /// 串口管理器接口
    /// 定义统一的串口通讯接口，支持不同协议的切换
    /// </summary>
    public interface ISerialPortManager : IDisposable
    {
        /// <summary>
        /// 示波器数据接收事件
        /// </summary>
        event Action<OscilloscopeData>? OscilloscopeDataReceived;

        /// <summary>
        /// Modbus驱动器数据更新事件
        /// </summary>
        event Action<ModbusDriverData>? ModbusDriverDataUpdated;

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        event Action<bool>? ConnectionStatusChanged;

        /// <summary>
        /// 获取当前协议类型
        /// </summary>
        ProtocolType CurrentProtocol { get; }

        /// <summary>
        /// 是否已连接
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 获取可用的串口列表
        /// </summary>
        /// <returns>串口名称列表</returns>
        string[] GetAvailablePorts();

        /// <summary>
        /// 根据视图名称智能连接串口
        /// </summary>
        /// <param name="viewName">视图名称</param>
        /// <param name="serialConfig">串口配置（示波器协议用）</param>
        /// <param name="modbusConfig">Modbus配置（Modbus协议用）</param>
        /// <returns>连接是否成功</returns>
        Task<bool> ConnectByViewAsync(string viewName, SerialPortConfig? serialConfig = null, ModbusConfig? modbusConfig = null);

        /// <summary>
        /// 连接串口（示波器协议）
        /// </summary>
        /// <param name="config">串口配置</param>
        /// <returns>连接是否成功</returns>
        Task<bool> ConnectOscilloscopeAsync(SerialPortConfig config);

        /// <summary>
        /// 连接Modbus设备
        /// </summary>
        /// <param name="config">Modbus配置</param>
        /// <returns>连接是否成功</returns>
        Task<bool> ConnectModbusAsync(ModbusConfig config);

        /// <summary>
        /// 断开连接
        /// </summary>
        Task DisconnectAsync();

        /// <summary>
        /// 发送数据（仅示波器协议）
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <returns>发送是否成功</returns>
        Task<bool> SendDataAsync(byte[] data);

        /// <summary>
        /// 获取Modbus协议处理器
        /// </summary>
        /// <returns>Modbus协议处理器，如果当前不是Modbus协议则返回null</returns>
        IModbusProtocolHandler? GetModbusHandler();

        /// <summary>
        /// 获取示波器协议处理器
        /// </summary>
        /// <returns>示波器协议处理器，如果当前不是示波器协议则返回null</returns>
        IOscilloscopeProtocolHandler? GetOscilloscopeHandler();
    }

    /// <summary>
    /// 协议类型枚举
    /// </summary>
    public enum ProtocolType
    {
        /// <summary>
        /// 无连接
        /// </summary>
        None,
        
        /// <summary>
        /// 示波器协议
        /// </summary>
        Oscilloscope,
        
        /// <summary>
        /// Modbus协议
        /// </summary>
        Modbus
    }
}