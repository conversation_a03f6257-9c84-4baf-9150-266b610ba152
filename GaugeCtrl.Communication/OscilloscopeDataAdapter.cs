using TouchSocket.Core;
using TouchSocket.SerialPorts;

namespace GaugeCtrl.Communication
{
    /// <summary>
    /// 示波器数据适配器
    /// 用于解析固定格式的示波器数据：0x55 + 两个UINT16 + 0xAA
    /// </summary>
    public class OscilloscopeDataAdapter : CustomFixedHeaderDataHandlingAdapter<OscilloscopeDataRequestInfo>
    {
        /// <summary>
        /// 接口实现，指示固定包头长度
        /// 在示波器协议中，包头为1字节（0x55）
        /// </summary>
        public override int HeaderLength => 1;

        /// <summary>
        /// 获取新的数据请求信息实例
        /// </summary>
        /// <returns>新的OscilloscopeDataRequestInfo实例</returns>
        protected override OscilloscopeDataRequestInfo GetInstance()
        {
            return new OscilloscopeDataRequestInfo();
        }
    }
}