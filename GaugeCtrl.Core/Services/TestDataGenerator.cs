using NLog;
using GaugeCtrl.Core.Models;

namespace GaugeCtrl.Core.Services
{
    /// <summary>
    /// 测试数据生成器
    /// 用于在没有实际硬件时生成模拟数据进行测试
    /// </summary>
    public class TestDataGenerator : IDisposable
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private readonly Timer _timer;
        private readonly Random _random = new();
        private bool _disposed = false;
        private double _time = 0;
        private const double FREQUENCY1 = 1.0; // 通道1频率 1Hz
        private const double FREQUENCY2 = 2.0; // 通道2频率 2Hz
        private const double AMPLITUDE1 = 30000; // 通道1幅度
        private const double AMPLITUDE2 = 25000; // 通道2幅度
        private const double OFFSET1 = 32768; // 通道1偏移
        private const double OFFSET2 = 32768; // 通道2偏移
        private const double NOISE_LEVEL = 500; // 噪声水平

        /// <summary>
        /// 测试数据生成事件
        /// </summary>
        public event Action<byte[]>? TestDataGenerated;

        /// <summary>
        /// 是否正在生成数据
        /// </summary>
        public bool IsGenerating { get; private set; }

        public TestDataGenerator()
        {
            _timer = new Timer(GenerateData, null, Timeout.Infinite, Timeout.Infinite);
        }

        /// <summary>
        /// 开始生成测试数据
        /// </summary>
        /// <param name="intervalMs">生成间隔（毫秒）</param>
        public void Start(int intervalMs = 50)
        {
            if (IsGenerating)
            {
                return;
            }

            IsGenerating = true;
            _time = 0;
            _timer.Change(0, intervalMs);
            Logger.Info($"开始生成测试数据，间隔: {intervalMs}ms");
        }

        /// <summary>
        /// 停止生成测试数据
        /// </summary>
        public void Stop()
        {
            if (!IsGenerating)
            {
                return;
            }

            IsGenerating = false;
            _timer.Change(Timeout.Infinite, Timeout.Infinite);
            Logger.Info("停止生成测试数据");
        }

        /// <summary>
        /// 生成单帧测试数据
        /// </summary>
        private void GenerateData(object? state)
        {
            try
            {
                // 生成正弦波数据
                var channel1Value = GenerateChannel1Value();
                var channel2Value = GenerateChannel2Value();

                // 构造数据帧：0x55 + UINT16(ch1) + UINT16(ch2) + 0xAA
                var frame = new byte[7];
                frame[0] = 0x55; // 帧头
                
                // 通道1数据（小端序）
                var ch1Bytes = BitConverter.GetBytes(channel1Value);
                frame[1] = ch1Bytes[0];
                frame[2] = ch1Bytes[1];
                
                // 通道2数据（小端序）
                var ch2Bytes = BitConverter.GetBytes(channel2Value);
                frame[3] = ch2Bytes[0];
                frame[4] = ch2Bytes[1];
                
                frame[5] = 0xAA; // 帧尾
                frame[6] = CalculateChecksum(frame, 5); // 简单校验和

                // 触发数据生成事件
                TestDataGenerated?.Invoke(frame);

                // 更新时间
                _time += 0.05; // 假设50ms间隔
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "生成测试数据时发生错误");
            }
        }

        /// <summary>
        /// 生成通道1数值（正弦波 + 噪声）
        /// </summary>
        private ushort GenerateChannel1Value()
        {
            var sineWave = Math.Sin(2 * Math.PI * FREQUENCY1 * _time) * AMPLITUDE1;
            var noise = (_random.NextDouble() - 0.5) * NOISE_LEVEL;
            var value = OFFSET1 + sineWave + noise;
            
            // 限制在UINT16范围内
            return (ushort)Math.Max(0, Math.Min(65535, value));
        }

        /// <summary>
        /// 生成通道2数值（余弦波 + 噪声）
        /// </summary>
        private ushort GenerateChannel2Value()
        {
            var cosineWave = Math.Cos(2 * Math.PI * FREQUENCY2 * _time) * AMPLITUDE2;
            var noise = (_random.NextDouble() - 0.5) * NOISE_LEVEL;
            var value = OFFSET2 + cosineWave + noise;
            
            // 限制在UINT16范围内
            return (ushort)Math.Max(0, Math.Min(65535, value));
        }

        /// <summary>
        /// 计算简单校验和
        /// </summary>
        private byte CalculateChecksum(byte[] data, int length)
        {
            byte checksum = 0;
            for (int i = 0; i < length; i++)
            {
                checksum ^= data[i];
            }
            return checksum;
        }

        /// <summary>
        /// 生成单个测试帧
        /// </summary>
        /// <returns>测试数据帧</returns>
        public byte[] GenerateSingleFrame()
        {
            var channel1Value = GenerateChannel1Value();
            var channel2Value = GenerateChannel2Value();

            var frame = new byte[7];
            frame[0] = 0x55;
            
            var ch1Bytes = BitConverter.GetBytes(channel1Value);
            frame[1] = ch1Bytes[0];
            frame[2] = ch1Bytes[1];
            
            var ch2Bytes = BitConverter.GetBytes(channel2Value);
            frame[3] = ch2Bytes[0];
            frame[4] = ch2Bytes[1];
            
            frame[5] = 0xAA;
            frame[6] = CalculateChecksum(frame, 5);

            _time += 0.05;
            return frame;
        }

        /// <summary>
        /// 重置时间
        /// </summary>
        public void ResetTime()
        {
            _time = 0;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                Stop();
                _timer?.Dispose();
                _disposed = true;
            }
        }
    }
}