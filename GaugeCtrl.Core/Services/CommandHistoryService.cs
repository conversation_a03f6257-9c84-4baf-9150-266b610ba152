using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using GaugeCtrl.Core.Interfaces;
using GaugeCtrl.Core.Models;
using NLog;

namespace GaugeCtrl.Core.Services
{
    /// <summary>
    /// 指令历史服务实现
    /// </summary>
    public class CommandHistoryService : ICommandHistoryService
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private readonly ObservableCollection<CommandHistory> _commandHistories;
        private readonly object _lockObject = new();

        /// <summary>
        /// 指令历史记录集合（只读）
        /// </summary>
        public ReadOnlyObservableCollection<CommandHistory> CommandHistories { get; }

        /// <summary>
        /// 最大历史记录数量
        /// </summary>
        public int MaxHistoryCount { get; set; } = 1000;

        /// <summary>
        /// 是否启用历史记录
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 历史记录变化事件
        /// </summary>
        public event EventHandler<CommandHistoryChangedEventArgs>? HistoryChanged;

        public CommandHistoryService()
        {
            _commandHistories = new ObservableCollection<CommandHistory>();
            CommandHistories = new ReadOnlyObservableCollection<CommandHistory>(_commandHistories);
        }

        /// <summary>
        /// 添加指令历史记录
        /// </summary>
        public void AddCommand(CommandHistory commandHistory)
        {
            if (!IsEnabled || commandHistory == null)
                return;

            lock (_lockObject)
            {
                try
                {
                    // 如果超过最大数量，移除最旧的记录
                    while (_commandHistories.Count >= MaxHistoryCount)
                    {
                        var oldestCommand = _commandHistories.OrderBy(c => c.Timestamp).FirstOrDefault();
                        if (oldestCommand != null)
                        {
                            _commandHistories.Remove(oldestCommand);
                        }
                    }

                    // 添加新记录到开头（最新的在前面）
                    _commandHistories.Insert(0, commandHistory);

                    Logger.Debug($"添加指令历史记录: {commandHistory.Description} - {commandHistory.HexData}");

                    // 触发事件
                    HistoryChanged?.Invoke(this, new CommandHistoryChangedEventArgs
                    {
                        ChangeType = HistoryChangeType.Added,
                        CommandHistory = commandHistory,
                        Count = 1
                    });
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "添加指令历史记录时发生错误");
                }
            }
        }

        /// <summary>
        /// 删除指定的指令历史记录
        /// </summary>
        public void RemoveCommand(CommandHistory commandHistory)
        {
            if (!IsEnabled || commandHistory == null)
                return;

            lock (_lockObject)
            {
                try
                {
                    if (_commandHistories.Remove(commandHistory))
                    {
                        Logger.Debug($"删除指令历史记录: {commandHistory.Description}");

                        // 触发事件
                        HistoryChanged?.Invoke(this, new CommandHistoryChangedEventArgs
                        {
                            ChangeType = HistoryChangeType.Removed,
                            CommandHistory = commandHistory,
                            Count = 1
                        });
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "删除指令历史记录时发生错误");
                }
            }
        }

        /// <summary>
        /// 删除多个指令历史记录
        /// </summary>
        public void RemoveCommands(IEnumerable<CommandHistory> commandHistories)
        {
            if (!IsEnabled || commandHistories == null)
                return;

            lock (_lockObject)
            {
                try
                {
                    var commandsToRemove = commandHistories.ToList();
                    var removedCount = 0;

                    foreach (var command in commandsToRemove)
                    {
                        if (_commandHistories.Remove(command))
                        {
                            removedCount++;
                        }
                    }

                    if (removedCount > 0)
                    {
                        Logger.Info($"删除了 {removedCount} 条指令历史记录");

                        // 触发事件
                        HistoryChanged?.Invoke(this, new CommandHistoryChangedEventArgs
                        {
                            ChangeType = HistoryChangeType.Removed,
                            Count = removedCount
                        });
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "删除多个指令历史记录时发生错误");
                }
            }
        }

        /// <summary>
        /// 更新指令执行结果
        /// </summary>
        public void UpdateCommandResult(string commandId, ModbusCommandStatus status, long executionTimeMs,
            string? errorMessage = null)
        {
            if (!IsEnabled || string.IsNullOrEmpty(commandId))
                return;

            lock (_lockObject)
            {
                try
                {
                    var command = _commandHistories.FirstOrDefault(c => c.Id == commandId);
                    if (command != null)
                    {
                        command.MarkCompleted(status, executionTimeMs, errorMessage);

                        Logger.Debug($"更新指令历史记录: {command.Description} - 状态: {status}, 耗时: {executionTimeMs}ms");

                        // 触发事件
                        HistoryChanged?.Invoke(this, new CommandHistoryChangedEventArgs
                        {
                            ChangeType = HistoryChangeType.Updated,
                            CommandHistory = command,
                            Count = 1
                        });
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "更新指令历史记录时发生错误");
                }
            }
        }

        /// <summary>
        /// 清除所有历史记录
        /// </summary>
        public void ClearHistory()
        {
            lock (_lockObject)
            {
                try
                {
                    var count = _commandHistories.Count;
                    _commandHistories.Clear();

                    Logger.Info($"清除了 {count} 条指令历史记录");

                    // 触发事件
                    HistoryChanged?.Invoke(this, new CommandHistoryChangedEventArgs
                    {
                        ChangeType = HistoryChangeType.Cleared,
                        Count = count
                    });
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "清除指令历史记录时发生错误");
                }
            }
        }

        /// <summary>
        /// 清除指定方向的历史记录
        /// </summary>
        public void ClearHistory(ModbusDirection direction)
        {
            lock (_lockObject)
            {
                try
                {
                    var commandsToRemove = _commandHistories.Where(c => c.Direction == direction).ToList();
                    foreach (var command in commandsToRemove)
                    {
                        _commandHistories.Remove(command);
                    }

                    Logger.Info($"清除了 {commandsToRemove.Count} 条 {direction} 方向的指令历史记录");

                    // 触发事件
                    HistoryChanged?.Invoke(this, new CommandHistoryChangedEventArgs
                    {
                        ChangeType = HistoryChangeType.Removed,
                        Count = commandsToRemove.Count
                    });
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "清除指定方向指令历史记录时发生错误");
                }
            }
        }

        /// <summary>
        /// 清除指定时间之前的历史记录
        /// </summary>
        public void ClearHistoryBefore(DateTime beforeTime)
        {
            lock (_lockObject)
            {
                try
                {
                    var commandsToRemove = _commandHistories.Where(c => c.Timestamp < beforeTime).ToList();
                    foreach (var command in commandsToRemove)
                    {
                        _commandHistories.Remove(command);
                    }

                    Logger.Info($"清除了 {commandsToRemove.Count} 条 {beforeTime:yyyy-MM-dd HH:mm:ss} 之前的指令历史记录");

                    // 触发事件
                    HistoryChanged?.Invoke(this, new CommandHistoryChangedEventArgs
                    {
                        ChangeType = HistoryChangeType.Removed,
                        Count = commandsToRemove.Count
                    });
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "清除指定时间之前的指令历史记录时发生错误");
                }
            }
        }

        /// <summary>
        /// 获取指定方向的历史记录
        /// </summary>
        public IEnumerable<CommandHistory> GetHistoryByDirection(ModbusDirection direction)
        {
            lock (_lockObject)
            {
                return _commandHistories.Where(c => c.Direction == direction).ToList();
            }
        }

        /// <summary>
        /// 获取指定功能码的历史记录
        /// </summary>
        public IEnumerable<CommandHistory> GetHistoryByFunctionCode(byte functionCode)
        {
            lock (_lockObject)
            {
                return _commandHistories.Where(c => c.FunctionCode == functionCode).ToList();
            }
        }

        /// <summary>
        /// 获取指定时间范围的历史记录
        /// </summary>
        public IEnumerable<CommandHistory> GetHistoryByTimeRange(DateTime startTime, DateTime endTime)
        {
            lock (_lockObject)
            {
                return _commandHistories.Where(c => c.Timestamp >= startTime && c.Timestamp <= endTime).ToList();
            }
        }

        /// <summary>
        /// 获取失败的历史记录
        /// </summary>
        public IEnumerable<CommandHistory> GetFailedCommands()
        {
            lock (_lockObject)
            {
                return _commandHistories.Where(c => c.Status == ModbusCommandStatus.Failed ||
                                                   c.Status == ModbusCommandStatus.Timeout ||
                                                   c.Status == ModbusCommandStatus.Exception).ToList();
            }
        }

        /// <summary>
        /// 导出历史记录到文件
        /// </summary>
        public async Task<bool> ExportHistoryAsync(string filePath, string format = "json")
        {
            try
            {
                List<CommandHistory> histories;
                lock (_lockObject)
                {
                    histories = _commandHistories.ToList();
                }

                switch (format.ToLower())
                {
                    case "json":
                        await ExportToJsonAsync(filePath, histories);
                        break;
                    case "csv":
                        await ExportToCsvAsync(filePath, histories);
                        break;
                    default:
                        throw new ArgumentException($"不支持的导出格式: {format}");
                }

                Logger.Info($"成功导出 {histories.Count} 条指令历史记录到文件: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"导出指令历史记录到文件 {filePath} 时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 从文件导入历史记录
        /// </summary>
        public async Task<int> ImportHistoryAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"文件不存在: {filePath}");
                }

                var extension = Path.GetExtension(filePath).ToLower();
                List<CommandHistory> importedHistories;

                switch (extension)
                {
                    case ".json":
                        importedHistories = await ImportFromJsonAsync(filePath);
                        break;
                    default:
                        throw new ArgumentException($"不支持的文件格式: {extension}");
                }

                lock (_lockObject)
                {
                    foreach (var history in importedHistories)
                    {
                        _commandHistories.Add(history);
                    }
                }

                Logger.Info($"成功从文件 {filePath} 导入 {importedHistories.Count} 条指令历史记录");
                return importedHistories.Count;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"从文件 {filePath} 导入指令历史记录时发生错误");
                return 0;
            }
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        public CommandHistoryStatistics GetStatistics()
        {
            lock (_lockObject)
            {
                var statistics = new CommandHistoryStatistics
                {
                    TotalCount = _commandHistories.Count,
                    SuccessCount = _commandHistories.Count(c => c.Status == ModbusCommandStatus.Success),
                    FailedCount = _commandHistories.Count(c => c.Status == ModbusCommandStatus.Failed),
                    TimeoutCount = _commandHistories.Count(c => c.Status == ModbusCommandStatus.Timeout),
                    AverageExecutionTimeMs = _commandHistories.Any() ? _commandHistories.Average(c => c.ExecutionTimeMs) : 0
                };

                // 统计各方向指令数量
                foreach (ModbusDirection direction in Enum.GetValues<ModbusDirection>())
                {
                    var count = _commandHistories.Count(c => c.Direction == direction);
                    if (count > 0)
                    {
                        statistics.DirectionStatistics[direction] = count;
                    }
                }

                // 统计各功能码指令数量
                var functionCodes = _commandHistories.Select(c => c.FunctionCode).Distinct();
                foreach (var functionCode in functionCodes)
                {
                    var count = _commandHistories.Count(c => c.FunctionCode == functionCode);
                    statistics.FunctionCodeStatistics[functionCode] = count;
                }

                return statistics;
            }
        }

        #region 私有方法

        private static async Task ExportToJsonAsync(string filePath, List<CommandHistory> histories)
        {
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            var json = JsonSerializer.Serialize(histories, options);
            await File.WriteAllTextAsync(filePath, json);
        }

        private static async Task ExportToCsvAsync(string filePath, List<CommandHistory> histories)
        {
            var lines = new List<string>
            {
                "时间,方向,功能码,16进制数据,描述,状态,耗时(ms),错误信息,从站地址,起始地址,数据长度"
            };

            foreach (var history in histories)
            {
                var line = $"{history.FormattedTimestamp},{history.DirectionDescription},{history.FunctionCode:X2}," +
                          $"{history.HexData},{history.Description},{history.StatusDescription},{history.ExecutionTimeMs}," +
                          $"{history.ErrorMessage},{history.SlaveId},{history.StartAddress:X4}," +
                          $"{history.DataLength}";
                lines.Add(line);
            }

            await File.WriteAllLinesAsync(filePath, lines);
        }

        private static async Task<List<CommandHistory>> ImportFromJsonAsync(string filePath)
        {
            var json = await File.ReadAllTextAsync(filePath);
            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            return JsonSerializer.Deserialize<List<CommandHistory>>(json, options) ?? new List<CommandHistory>();
        }

        #endregion
    }

    /// <summary>
    /// 指令历史服务单例
    /// </summary>
    public static class CommandHistoryServiceSingleton
    {
        private static readonly Lazy<ICommandHistoryService> _instance =
            new(() => new CommandHistoryService());

        /// <summary>
        /// 获取指令历史服务实例
        /// </summary>
        public static ICommandHistoryService Instance => _instance.Value;
    }
}
