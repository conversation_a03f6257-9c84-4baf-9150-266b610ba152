using System.ComponentModel;
using System.Runtime.CompilerServices;
using GaugeCtrl.Core.Interfaces;

namespace GaugeCtrl.Core.Models
{
    /// <summary>
    /// 示波器数据模型
    /// </summary>
    public class OscilloscopeData : INotifyPropertyChanged
    {
        private ushort _channel1Value;
        private ushort _channel2Value;

        /// <summary>
        /// 通道1数值
        /// </summary>
        public ushort Channel1Value
        {
            get => _channel1Value;
            set
            {
                _channel1Value = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 通道2数值
        /// </summary>
        public ushort Channel2Value
        {
            get => _channel2Value;
            set
            {
                _channel2Value = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 串口配置模型
    /// </summary>
    public class SerialPortConfig : IConnectionConfig, INotifyPropertyChanged
    {
        private string _portName = "COM1";
        private int _baudRate = 230400;
        private int _dataBits = 8;
        private System.IO.Ports.Parity _parity = System.IO.Ports.Parity.None;
        private System.IO.Ports.StopBits _stopBits = System.IO.Ports.StopBits.One;
        private byte _slaveId = 1;
        private bool _isConnected;

        /// <summary>
        /// 端口名称
        /// </summary>
        public string PortName
        {
            get => _portName;
            set
            {
                _portName = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 波特率
        /// </summary>
        public int BaudRate
        {
            get => _baudRate;
            set
            {
                _baudRate = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 数据位
        /// </summary>
        public int DataBits
        {
            get => _dataBits;
            set
            {
                _dataBits = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 校验位
        /// </summary>
        public System.IO.Ports.Parity Parity
        {
            get => _parity;
            set
            {
                _parity = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 停止位
        /// </summary>
        public System.IO.Ports.StopBits StopBits
        {
            get => _stopBits;
            set
            {
                _stopBits = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 从站地址（Modbus）
        /// </summary>
        public byte SlaveId
        {
            get => _slaveId;
            set
            {
                _slaveId = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 连接状态
        /// </summary>
        public bool IsConnected
        {
            get => _isConnected;
            set
            {
                _isConnected = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}