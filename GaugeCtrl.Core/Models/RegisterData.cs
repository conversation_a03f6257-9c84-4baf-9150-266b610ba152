using CommunityToolkit.Mvvm.ComponentModel;

namespace GaugeCtrl.Core.Models
{
    /// <summary>
    /// 寄存器数据模型
    /// </summary>
    public partial class RegisterData : ObservableObject
    {
        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(AddressHex))]
        private int addressDec;

        public string AddressHex => $"0x{AddressDec:X4}";

        [ObservableProperty]
        [NotifyPropertyChangedFor(nameof(ValueHex))]
        private int valueDec;

        public string ValueHex => $"0x{ValueDec:X4}";
    }
}