using CommunityToolkit.Mvvm.ComponentModel;
using System.ComponentModel;
using System.Collections.Generic;
using System;

namespace GaugeCtrl.Core.Models
{
    /// <summary>
    /// 参数类型枚举
    /// </summary>
    public enum ParameterType
    {
        /// <summary>
        /// 数值类型
        /// </summary>
        Numeric,
        /// <summary>
        /// 布尔类型
        /// </summary>
        <PERSON><PERSON><PERSON>,
        /// <summary>
        /// 枚举类型
        /// </summary>
        Enum,
        /// <summary>
        /// 文本类型
        /// </summary>
        Text
    }

    /// <summary>
    /// 轴枚举
    /// </summary>
    public enum AxisType
    {
        /// <summary>
        /// 轴0
        /// </summary>
        Axis0,
        /// <summary>
        /// 轴1
        /// </summary>
        Axis1
    }

    /// <summary>
    /// 参数类型枚举
    /// </summary>
    public enum ParameterCategory
    {
        /// <summary>
        /// IO参数
        /// </summary>
        IO,
        /// <summary>
        /// 保护参数
        /// </summary>
        Protection,
        /// <summary>
        /// 控制参数
        /// </summary>
        Control,
        /// <summary>
        /// 运动参数
        /// </summary>
        Motion,
        /// <summary>
        /// 电机参数
        /// </summary>
        Motor,
        /// <summary>
        /// 监控参数
        /// </summary>
        Monitoring
    }

    /// <summary>
    /// 通用参数类，适用于所有参数类型
    /// </summary>
    public partial class Parameter : ObservableObject, IDataErrorInfo
    {
        [ObservableProperty]
        private string _name;

        private object _value;
        [ObservableProperty]
        private object _originalValue;

        [ObservableProperty]
        private string _unit;

        [ObservableProperty]
        private object _minValue;

        [ObservableProperty]
        private object _maxValue;

        [ObservableProperty]
        private ParameterType _parameterType;

        [ObservableProperty]
        private List<object> _enumValues;

        [ObservableProperty]
        private bool _isModified;

        [ObservableProperty]
        private bool _isEnabled = true;

        [ObservableProperty]
        private ushort _registerAddress;

        [ObservableProperty]
        private ParameterCategory _category;

        /// <summary>
        /// 参数值
        /// </summary>
        public object Value
        {
            get => _value;
            set
            {
                var oldValue = _value;
                if (SetProperty(ref _value, value))
                {
                    IsModified = !Equals(_originalValue, value);
                }
            }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public Parameter(string name, object value, ParameterCategory category, string unit = "", object minValue = null, object maxValue = null, ParameterType parameterType = ParameterType.Numeric, List<object> enumValues = null, ushort registerAddress = 0)
        {
            Name = name;
            _originalValue = value;
            _value = value;
            Category = category;
            Unit = unit;
            MinValue = minValue;
            MaxValue = maxValue;
            ParameterType = parameterType;
            EnumValues = enumValues ?? new List<object>();
            IsModified = false;
            RegisterAddress = registerAddress;
        }

        /// <summary>
        /// IDataErrorInfo 实现
        /// </summary>
        public string Error => null;

        /// <summary>
        /// IDataErrorInfo 实现
        /// </summary>
        public string this[string columnName]
        {
            get
            {
                if (columnName == nameof(Value) && ParameterType == ParameterType.Numeric)
                {
                    return ValidateNumericValue();
                }
                return null;
            }
        }

        /// <summary>
        /// 验证数值类型的值
        /// </summary>
        private string ValidateNumericValue()
        {
            if (Value == null)
            {
                // 电机参数不允许空值
                if (Category == ParameterCategory.Motor)
                {
                    return "值不能为空";
                }
                return null;
            }

            if (double.TryParse(Value.ToString(), out double numericValue))
            {
                // 检查最小值
                if (MinValue != null && double.TryParse(MinValue.ToString(), out double min) && numericValue < min)
                {
                    return $"值不能小于 {MinValue}";
                }

                // 检查最大值
                if (MaxValue != null && double.TryParse(MaxValue.ToString(), out double max) && numericValue > max)
                {
                    return $"值不能大于 {MaxValue}";
                }

                // 检查16位无符号整数范围（除了IO和监控参数）
                if (Category != ParameterCategory.IO && Category != ParameterCategory.Monitoring)
                {
                    if (numericValue < 0 || numericValue > 65535)
                    {
                        return "值必须在0-65535范围内";
                    }
                }
            }
            else if (ParameterType == ParameterType.Numeric)
            {
                return "请输入有效的数值";
            }

            return null;
        }
    }




}