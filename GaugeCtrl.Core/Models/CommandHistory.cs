using System;
using System.ComponentModel;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;

namespace GaugeCtrl.Core.Models
{
    /// <summary>
    /// Modbus 指令方向枚举
    /// </summary>
    public enum ModbusDirection
    {
        [Description("发送")]
        Send,

        [Description("接收")]
        Receive
    }

    /// <summary>
    /// Modbus 指令状态枚举
    /// </summary>
    public enum ModbusCommandStatus
    {
        [Description("成功")]
        Success,

        [Description("失败")]
        Failed,

        [Description("超时")]
        Timeout,

        [Description("异常")]
        Exception
    }

    /// <summary>
    /// Modbus 指令历史记录模型
    /// </summary>
    public partial class CommandHistory : ObservableObject
    {
        /// <summary>
        /// 唯一标识符
        /// </summary>
        [ObservableProperty]
        private string id = Guid.NewGuid().ToString();

        /// <summary>
        /// 时间戳
        /// </summary>
        [ObservableProperty]
        private DateTime timestamp = DateTime.Now;

        /// <summary>
        /// 指令方向（发送/接收）
        /// </summary>
        [ObservableProperty]
        private ModbusDirection direction;

        /// <summary>
        /// 16进制指令数据
        /// </summary>
        [ObservableProperty]
        private string hexData = string.Empty;

        /// <summary>
        /// 指令状态
        /// </summary>
        [ObservableProperty]
        private ModbusCommandStatus status;

        /// <summary>
        /// 执行耗时（毫秒）
        /// </summary>
        [ObservableProperty]
        private long executionTimeMs;

        /// <summary>
        /// 错误信息（如果有）
        /// </summary>
        [ObservableProperty]
        private string? errorMessage;

        /// <summary>
        /// 从站地址
        /// </summary>
        [ObservableProperty]
        private byte slaveId;

        /// <summary>
        /// 功能码
        /// </summary>
        [ObservableProperty]
        private byte functionCode;

        /// <summary>
        /// 起始地址
        /// </summary>
        [ObservableProperty]
        private ushort? startAddress;

        /// <summary>
        /// 数据长度或数量
        /// </summary>
        [ObservableProperty]
        private ushort? dataLength;

        /// <summary>
        /// 指令描述
        /// </summary>
        [ObservableProperty]
        private string description = string.Empty;

        /// <summary>
        /// 格式化的时间字符串
        /// </summary>
        public string FormattedTimestamp => Timestamp.ToString("HH:mm:ss.fff");

        /// <summary>
        /// 格式化的执行时间字符串
        /// </summary>
        public string FormattedExecutionTime => ExecutionTimeMs < 1000
            ? $"{ExecutionTimeMs}ms"
            : $"{ExecutionTimeMs / 1000.0:F2}s";

        /// <summary>
        /// 方向符号
        /// </summary>
        public string DirectionSymbol => Direction == ModbusDirection.Send ? "->" : "<-";

        /// <summary>
        /// 状态颜色（用于UI显示）
        /// </summary>
        public string StatusColor => Status switch
        {
            ModbusCommandStatus.Success => "Green",
            ModbusCommandStatus.Failed => "Red",
            ModbusCommandStatus.Timeout => "Orange",
            ModbusCommandStatus.Exception => "Purple",
            _ => "Black"
        };

        /// <summary>
        /// 方向描述
        /// </summary>
        public string DirectionDescription => GetEnumDescription(Direction);

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDescription => GetEnumDescription(Status);

        /// <summary>
        /// 功能码描述
        /// </summary>
        public string FunctionCodeDescription => FunctionCode switch
        {
            0x01 => "读取线圈状态",
            0x02 => "读取离散输入状态",
            0x03 => "读取保持寄存器",
            0x04 => "读取输入寄存器",
            0x05 => "写入单个线圈",
            0x06 => "写入单个寄存器",
            0x0F => "写入多个线圈",
            0x10 => "写入多个寄存器",
            _ => $"功能码 0x{FunctionCode:X2}"
        };

        /// <summary>
        /// 获取枚举描述
        /// </summary>
        private static string GetEnumDescription(Enum value)
        {
            var field = value.GetType().GetField(value.ToString());
            if (field?.GetCustomAttributes(typeof(DescriptionAttribute), false) is DescriptionAttribute[] attributes && attributes.Length > 0)
            {
                return attributes[0].Description;
            }
            return value.ToString();
        }

        /// <summary>
        /// 创建发送指令历史记录
        /// </summary>
        public static CommandHistory CreateSendCommand(byte slaveId, byte functionCode, byte[] data, string description = "")
        {
            return new CommandHistory
            {
                Direction = ModbusDirection.Send,
                SlaveId = slaveId,
                FunctionCode = functionCode,
                HexData = BytesToHexString(data),
                Description = string.IsNullOrEmpty(description) ? GetDefaultDescription(functionCode) : description,
                Status = ModbusCommandStatus.Success
            };
        }

        /// <summary>
        /// 创建接收指令历史记录
        /// </summary>
        public static CommandHistory CreateReceiveCommand(byte slaveId, byte functionCode, byte[] data, string description = "")
        {
            return new CommandHistory
            {
                Direction = ModbusDirection.Receive,
                SlaveId = slaveId,
                FunctionCode = functionCode,
                HexData = BytesToHexString(data),
                Description = string.IsNullOrEmpty(description) ? GetDefaultDescription(functionCode) : description,
                Status = ModbusCommandStatus.Success
            };
        }

        /// <summary>
        /// 创建错误指令历史记录
        /// </summary>
        public static CommandHistory CreateErrorCommand(byte slaveId, byte functionCode, string errorMessage, string description = "")
        {
            return new CommandHistory
            {
                Direction = ModbusDirection.Send,
                SlaveId = slaveId,
                FunctionCode = functionCode,
                HexData = "",
                Description = string.IsNullOrEmpty(description) ? GetDefaultDescription(functionCode) : description,
                Status = ModbusCommandStatus.Failed,
                ErrorMessage = errorMessage
            };
        }

        /// <summary>
        /// 将字节数组转换为16进制字符串
        /// </summary>
        private static string BytesToHexString(byte[] bytes)
        {
            if (bytes == null || bytes.Length == 0)
                return "";

            return string.Join(" ", bytes.Select(b => $"{b:X2}"));
        }

        /// <summary>
        /// 获取功能码的默认描述
        /// </summary>
        private static string GetDefaultDescription(byte functionCode)
        {
            return functionCode switch
            {
                0x01 => "读取线圈状态",
                0x02 => "读取离散输入状态",
                0x03 => "读取保持寄存器",
                0x04 => "读取输入寄存器",
                0x05 => "写入单个线圈",
                0x06 => "写入单个寄存器",
                0x0F => "写入多个线圈",
                0x10 => "写入多个寄存器",
                _ => $"功能码 0x{functionCode:X2}"
            };
        }

        /// <summary>
        /// 标记指令执行完成
        /// </summary>
        public void MarkCompleted(ModbusCommandStatus status, long executionTimeMs, string? errorMessage = null)
        {
            Status = status;
            ExecutionTimeMs = executionTimeMs;
            ErrorMessage = errorMessage;
        }
    }
}
