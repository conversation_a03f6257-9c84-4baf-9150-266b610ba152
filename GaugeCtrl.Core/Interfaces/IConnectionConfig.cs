namespace GaugeCtrl.Core.Interfaces
{
    /// <summary>
    /// 连接配置基础接口
    /// </summary>
    public interface IConnectionConfig
    {
        /// <summary>
        /// 端口名称
        /// </summary>
        string PortName { get; set; }

        /// <summary>
        /// 波特率
        /// </summary>
        int BaudRate { get; set; }

        /// <summary>
        /// 是否已连接
        /// </summary>
        bool IsConnected { get; set; }
    }
}