using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using GaugeCtrl.Core.Models;

namespace GaugeCtrl.Core.Interfaces
{
    /// <summary>
    /// 指令历史服务接口
    /// </summary>
    public interface ICommandHistoryService
    {
        /// <summary>
        /// 指令历史记录集合（只读）
        /// </summary>
        ReadOnlyObservableCollection<CommandHistory> CommandHistories { get; }

        /// <summary>
        /// 最大历史记录数量
        /// </summary>
        int MaxHistoryCount { get; set; }

        /// <summary>
        /// 是否启用历史记录
        /// </summary>
        bool IsEnabled { get; set; }

        /// <summary>
        /// 添加指令历史记录
        /// </summary>
        /// <param name="commandHistory">指令历史记录</param>
        void AddCommand(CommandHistory commandHistory);

        /// <summary>
        /// 删除指定的指令历史记录
        /// </summary>
        /// <param name="commandHistory">要删除的指令历史记录</param>
        void RemoveCommand(CommandHistory commandHistory);

        /// <summary>
        /// 删除多个指令历史记录
        /// </summary>
        /// <param name="commandHistories">要删除的指令历史记录列表</param>
        void RemoveCommands(IEnumerable<CommandHistory> commandHistories);

        /// <summary>
        /// 更新指令执行结果
        /// </summary>
        /// <param name="commandId">指令ID</param>
        /// <param name="status">执行状态</param>
        /// <param name="executionTimeMs">执行耗时</param>
        /// <param name="errorMessage">错误信息</param>
        void UpdateCommandResult(string commandId, ModbusCommandStatus status, long executionTimeMs,
            string? errorMessage = null);

        /// <summary>
        /// 清除所有历史记录
        /// </summary>
        void ClearHistory();

        /// <summary>
        /// 清除指定方向的历史记录
        /// </summary>
        /// <param name="direction">指令方向</param>
        void ClearHistory(ModbusDirection direction);

        /// <summary>
        /// 清除指定时间之前的历史记录
        /// </summary>
        /// <param name="beforeTime">时间点</param>
        void ClearHistoryBefore(DateTime beforeTime);

        /// <summary>
        /// 获取指定方向的历史记录
        /// </summary>
        /// <param name="direction">指令方向</param>
        /// <returns>历史记录列表</returns>
        IEnumerable<CommandHistory> GetHistoryByDirection(ModbusDirection direction);

        /// <summary>
        /// 获取指定功能码的历史记录
        /// </summary>
        /// <param name="functionCode">功能码</param>
        /// <returns>历史记录列表</returns>
        IEnumerable<CommandHistory> GetHistoryByFunctionCode(byte functionCode);

        /// <summary>
        /// 获取指定时间范围的历史记录
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>历史记录列表</returns>
        IEnumerable<CommandHistory> GetHistoryByTimeRange(DateTime startTime, DateTime endTime);

        /// <summary>
        /// 获取失败的历史记录
        /// </summary>
        /// <returns>失败的历史记录列表</returns>
        IEnumerable<CommandHistory> GetFailedCommands();

        /// <summary>
        /// 导出历史记录到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="format">导出格式（json, csv, xml）</param>
        /// <returns>是否导出成功</returns>
        Task<bool> ExportHistoryAsync(string filePath, string format = "json");

        /// <summary>
        /// 从文件导入历史记录
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>导入的记录数量</returns>
        Task<int> ImportHistoryAsync(string filePath);

        /// <summary>
        /// 获取统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        CommandHistoryStatistics GetStatistics();

        /// <summary>
        /// 历史记录变化事件
        /// </summary>
        event EventHandler<CommandHistoryChangedEventArgs>? HistoryChanged;
    }

    /// <summary>
    /// 指令历史统计信息
    /// </summary>
    public class CommandHistoryStatistics
    {
        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 成功记录数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败记录数
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// 超时记录数
        /// </summary>
        public int TimeoutCount { get; set; }

        /// <summary>
        /// 平均执行时间（毫秒）
        /// </summary>
        public double AverageExecutionTimeMs { get; set; }

        /// <summary>
        /// 成功率（百分比）
        /// </summary>
        public double SuccessRate => TotalCount > 0 ? (double)SuccessCount / TotalCount * 100 : 0;

        /// <summary>
        /// 各方向指令统计
        /// </summary>
        public Dictionary<ModbusDirection, int> DirectionStatistics { get; set; } = new();

        /// <summary>
        /// 各功能码指令统计
        /// </summary>
        public Dictionary<byte, int> FunctionCodeStatistics { get; set; } = new();
    }

    /// <summary>
    /// 指令历史变化事件参数
    /// </summary>
    public class CommandHistoryChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 变化类型
        /// </summary>
        public HistoryChangeType ChangeType { get; set; }

        /// <summary>
        /// 相关的指令历史记录
        /// </summary>
        public CommandHistory? CommandHistory { get; set; }

        /// <summary>
        /// 变化数量
        /// </summary>
        public int Count { get; set; }
    }

    /// <summary>
    /// 历史记录变化类型
    /// </summary>
    public enum HistoryChangeType
    {
        Added,
        Updated,
        Removed,
        Cleared
    }
}
